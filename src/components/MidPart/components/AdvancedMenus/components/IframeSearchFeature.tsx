import useStateManager from '../../../../../store/stateManager.hook';
import MenuDropdownItem from '../../MenuDropdownItem';

function IframeSearchFeature() {
  const { globalSettings } = useStateManager();

  return (
    <MenuDropdownItem
      onYes={() => globalSettings.onsIsIframeSearch(true)}
      onNo={() => globalSettings.onsIsIframeSearch(false)}
      isYes={globalSettings.iframeSearch.isOn}
      label="iFrame search"
      labelTooltip="It can significantly reduce the experience of web browsing. May not work on all webpages."
    />
  );
}

export default IframeSearchFeature;
