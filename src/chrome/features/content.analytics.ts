import { GlobalSettings, QueryItem } from "../../definitions/global.definition";
import { ChromeMessage, Sender } from "../../types";
import {
  getStorageDataLocal,
  sendMessageTo,
  setStorageDataLocal,
} from "../chrome.utils";
import { hilityTag } from "../content.consts";

export interface AnalyticsData {
  url: string;
  visitTime: string;
  data: ReadableAnalyticsData[];
  totalSum: number;
}

export interface ReadableAnalyticsData {
  queryItem: QueryItem;
  collectedData: {
    number: number;
  };
}

export async function collectAnalyticsData(
  currentGlobalSettings: GlobalSettings,
  isObserver?: boolean,
  isMessagingTrigger?: boolean
) {
  if (typeof window === "undefined" || !currentGlobalSettings?.analytics?.isOn)
    return;
  const currrentList =
    currentGlobalSettings.queryListState.selectedList.itemList;

  const allFoundElements = document.getElementsByTagName(hilityTag);

  if (!allFoundElements.length) return;

  const collectById = sortByIdFoundElements(allFoundElements);

  const getReadableData: ReadableAnalyticsData[] = currrentList.map(
    (queryItem) => ({
      queryItem: queryItem,
      collectedData: collectById?.[queryItem.id],
    })
  );

  const currentAnalyticsData: AnalyticsData = {
    url: window.location.href,
    visitTime: getTime(),
    data: getReadableData,
    totalSum: totalSum(getReadableData),
  };

  const oldData = await getAnalyticsFromStorage();

  if (
    hasQueryDeleted(oldData, currentAnalyticsData, currrentList) ||
    (compareCurrentLastData(currentAnalyticsData, oldData[0]) &&
      isMessagingTrigger)
  ) {
    return;
  }

  if (isObserver && oldData.length) {
    // find the latest url
    let hasBeenModified = false;
    const updatedAnalytics = oldData.map((analyticData) => {
      if (analyticData.url === currentAnalyticsData.url && !hasBeenModified) {
        hasBeenModified = true;
        return currentAnalyticsData;
      }
      return analyticData;
    });

    setAnalyticsInStorage(updatedAnalytics);
    return;
  }

  const analyticsData: AnalyticsData[] = oldData
    ? [currentAnalyticsData, ...oldData]
    : [currentAnalyticsData];

  setAnalyticsInStorage(analyticsData);
}

/// limit data
/// clean data

function getTime() {
  const locale = getClientLocale();

  const options = {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  } as any;

  return new Date().toLocaleString(locale ?? "en-US", options);
}

function getClientLocale() {
  if (typeof Intl !== "undefined") {
    try {
      return Intl.NumberFormat().resolvedOptions().locale;
    } catch (err) {
      console.error("Cannot get locale from Intl");
    }
  }
}

async function getAnalyticsFromStorage() {
  const oldAnalyticsData = (await getStorageDataLocal("analyticsData")) as {
    analyticsData: AnalyticsData[];
  };

  const oldData = oldAnalyticsData.analyticsData;

  return oldData ?? [];
}

async function setAnalyticsInStorage(analyticsData: AnalyticsData[]) {
  await setStorageDataLocal<AnalyticsData[]>({ analyticsData: analyticsData });
  await sendUpdateToPopup(analyticsData);
  return;
}

function compareCurrentLastData(
  currentData: AnalyticsData,
  lastData: AnalyticsData
) {
  if (!lastData) return false;

  const isUrlSame = currentData.url === lastData.url;
  const areQueriesSame =
    currentData.data.length === lastData.data.length ||
    currentData.data.length <= lastData.data.length;

  return isUrlSame && areQueriesSame;
}

function sortByIdFoundElements(allFoundElements: HTMLCollectionOf<Element>) {
  const collectById = {} as Record<string, { number: number }>;
  for (const element of allFoundElements) {
    const id = element.getAttribute("data-hility");
    if (id) {
      if (collectById[id]) {
        collectById[id].number++;
      } else {
        collectById[id] = { number: 1 };
      }
    }
  }

  //   const collectById = Array.from(allFoundElements).reduce((acc, curr) => {
  //     const id = curr.getAttribute('data-hility') as string;
  //     if (id && acc?.[id])
  //       return (acc = { ...acc, [id]: { number: acc?.[id].number + 1 } });

  //     return (acc = { ...acc, [id]: { number: 1 } });
  //   }, {} as Record<string, { number: number }>);

  return collectById;
}

function totalSum(readableData: ReadableAnalyticsData[]) {
  return readableData.reduce(
    (acc, curr) =>
      curr?.collectedData?.number ? acc + curr?.collectedData?.number : acc,
    0
  );
}

async function sendUpdateToPopup(analyticsData: AnalyticsData[]) {
  const message: ChromeMessage = {
    from: Sender.Analytics,
    message: { analyticsData: analyticsData },
  };

  await sendMessageTo(message);
}

function hasQueryDeleted(
  oldData: AnalyticsData[],
  currentAnalyticsData: AnalyticsData,
  currrentList: QueryItem[]
) {
  // if a query has been removed in the popup, don't run the analytics again
  if (!oldData?.length) return false;

  const previousData = oldData[0];

  return (
    previousData?.url === currentAnalyticsData.url &&
    currrentList?.length < previousData?.data?.length
  );
}
