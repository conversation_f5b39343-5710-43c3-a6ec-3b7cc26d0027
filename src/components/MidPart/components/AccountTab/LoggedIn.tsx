import { But<PERSON>, Flex, Heading, Text } from "@chakra-ui/react";
import { UserSubscription } from "../../../../definitions/global.definition";
import useStateManager from "../../../../store/stateManager.hook";

import useCustomToast from "../../../shared/components/Toast/CustomToast.hook";
import DeleteAccount from "./DeleteAccount";
import { onSignOut, sendVerificationAgain } from "./firebase.utils";
import LoggedInTextWrapper from "./LoggedInTextWrapper";
import Subscription from "./Subscription";

function LoggedIn() {
  const customToast = useCustomToast();

  const {
    account: { account, onSetIsAccountLoading, onSetAccount },
    subscription: { subscription, onSetSubscription },
  } = useStateManager();

  function subscriptionStatus(subscription: UserSubscription) {
    switch (subscription?.subscriptionStatus) {
      case "trialing":
        return "PRO 👑 - TRIAL";
      case "active":
        return "PRO 👑";
      case "deleted":
        return subscription.isActive ? "PRO 👑 - CANCELED" : "Free";
      default:
        return `Free`;
    }
  }

  return (
    <Flex w="100%" align="center" flexDir={"column"}>
      <Heading mt="0" textTransform={"uppercase"} fontSize={"13px"}>
        Signed In
      </Heading>

      <LoggedInTextWrapper title="Account" data={account?.email} />

      <LoggedInTextWrapper
        title="Tier type"
        data={subscriptionStatus(subscription)}
      />

      {subscription?.currentPeriodEnd !== "Invalid Date" &&
      subscription?.isActive &&
      !subscription?.isCanceled ? (
        <LoggedInTextWrapper
          title="Next bill date"
          data={subscription?.currentPeriodEnd}
        />
      ) : null}

      {subscription?.currentPeriodEnd !== "Invalid Date" &&
      subscription?.isActive &&
      subscription?.isCanceled ? (
        <LoggedInTextWrapper
          title="Valid until"
          data={subscription?.currentPeriodEnd}
        />
      ) : null}

      <DeleteAccount />

      <Subscription />

      {account?.emailVerified ? null : (
        <Text
          cursor={"pointer"}
          onClick={() => sendVerificationAgain(customToast)}
          textAlign="center"
          fontSize="10px"
          textDecor={"none"}
          opacity="0.75"
          my="1"
          as="b"
          color="#007bff"
        >
          Confirmation not received? Send email again.
        </Text>
      )}
      <Flex wrap={"wrap"} mt="2" w="100%" justify={"space-evenly"}>
        <Button
          onClick={() =>
            onSignOut(
              customToast,
              onSetIsAccountLoading,
              onSetSubscription,
              onSetAccount
            )
          }
          size="xs"
        >
          Sign Out
        </Button>
      </Flex>
    </Flex>
  );
}

export default LoggedIn;
