import useStateManager from '../../../../../store/stateManager.hook';
import { disablePopupVisualFeatures } from '../../../../shared/utils/popupStateInterceptor';
import MenuDropdownItem from '../../MenuDropdownItem';

function TotalFoundBadgeFeature({ isDisabled }: { isDisabled?: boolean }) {
  const { globalSettings } = useStateManager();

  return (
    <MenuDropdownItem
      onYes={() => globalSettings.onSetIsBadgeFoundSum(true)}
      onNo={() => globalSettings.onSetIsBadgeFoundSum(false)}
      isYes={disablePopupVisualFeatures(
        isDisabled,
        globalSettings.badgeFoundSum.isOn
      )}
      label="Found sum. on badge"
      isFeatureDisabled={isDisabled}
      labelTooltip="Show the total amount of found queries on the extension icon. Each tab has it's own counter"
    />
  );
}

export default TotalFoundBadgeFeature;
