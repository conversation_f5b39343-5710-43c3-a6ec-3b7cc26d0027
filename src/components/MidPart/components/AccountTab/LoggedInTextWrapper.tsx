import { Flex, Text } from '@chakra-ui/react';

interface Props {
  title: string;
  data: any;
}

function LoggedInTextWrapper({ title, data }: Props) {
  return (
    <Flex
      pt="1"
      px="1"
      w="100%"
      align="start"
      justify="flex-start"
      textAlign="start"
      flexDir={'column'}
      fontSize="11.5px"
    >
      <Text fontWeight="500">{title}:</Text>
      <Flex align="center" w="100%" justify="space-between" fontSize={'11px'}>
        {data}
      </Flex>
    </Flex>
  );
}

export default LoggedInTextWrapper;
