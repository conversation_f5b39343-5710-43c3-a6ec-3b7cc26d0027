import useStateManager from '../../../../../store/stateManager.hook';
import MenuDropdownItem from '../../MenuDropdownItem';

function InputFocusFeature() {
  const {
    inputFocus: { onIsInputFocus, isInputFocus },
  } = useStateManager();

  return (
    <MenuDropdownItem
      onYes={() => onIsInputFocus(true)}
      onNo={() => onIsInputFocus(false)}
      isYes={isInputFocus}
      label="Focus input on startup"
      labelTooltip="Focus without clicking on the query input when opening the extension popup."
    />
  );
}

export default InputFocusFeature;
