export enum Sender {
  React,
  Content,
  SummaryBadge,
  Analytics,
}

export interface ChromeMessage {
  from: Sender;
  message: any;
}
export interface ReactMessage {
  from: Sender;
  message: { data: any; command: string };
}

export interface BgToPopupResponse {
  type: string; // result
  status: string; // error - success
  data: any; // anything that the function returns
  request: ReactMessage;
}
