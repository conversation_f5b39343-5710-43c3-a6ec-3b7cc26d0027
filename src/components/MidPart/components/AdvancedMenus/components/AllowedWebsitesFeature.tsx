import { Textarea } from '@chakra-ui/react';
import { ChangeEvent } from 'react';
import useStateManager from '../../../../../store/stateManager.hook';
import { blueInputStyle } from '../../../../../styles/global.styles';
import MenuDropdownItem from '../../MenuDropdownItem';

function AllowedWebsitesFeature() {
  // whitelistSites.websiteList.includes(window.location.host)

  const {
    whiteList,
    globalSettings: { whitelistSites },
  } = useStateManager();

  function onChange(e: ChangeEvent<HTMLTextAreaElement>) {
    whiteList.onSetWhiteListSites(e.target.value);
  }

  return (
    <MenuDropdownItem
      labelTooltip="The extension will only work on these webpages. Separate the website domains by columns."
      isMore
      //   onMore={() => null}
      label="Allowed websites"
      isYes={whitelistSites.isOn}
      onYes={() => whiteList.onIsWhiteListSite(true)}
      onNo={() => whiteList.onIsWhiteListSite(false)}
    >
      <Textarea
        onChange={onChange}
        p="1"
        maxH={'120px'}
        fontSize={'10.5px'}
        placeholder="google.com,linkedin.com"
        value={whitelistSites.websiteList}
        {...blueInputStyle}
      ></Textarea>
    </MenuDropdownItem>
  );
}

export default AllowedWebsitesFeature;
