import {
  GlobalSettings,
  NavigationBar,
  QueryItem
} from '../../definitions/global.definition';
import { dataHility, hilityTag } from '../content.consts';

// let isAlreadyRunning = false;
let currentNavIndex = 0;
let elements = [] as HTMLElement[];
let currentWordId: string;

let navigationListener = null as unknown as AbortController;

const navigationItemClass = 'hility-navigation';
const navigationItemCssId = 'hility-navigation-style';

let navigationState: NavigationBar;

export function navigation(
  globalSettings: GlobalSettings,
  isObserver?: boolean,
  isMessagingTrigger?: boolean
) {
  const item = globalSettings.navigationBar?.focusedWord;

  elements = filterHiddenElements(item);

  // console.log(elements, 'elements');
  // console.log(currentNavIndex, 'currentNavIndex');

  if (!elements?.length || isObserver) return;

  const isNavigation = globalSettings.navigationBar.isOn;

  if (!navigationState) setNavigationState(globalSettings.navigationBar);

  const down = globalSettings.navigationBar.assignedButtons.downButtonKey;
  const up = globalSettings.navigationBar.assignedButtons.upButtonKey;

  // console.log('navigation init');

  const navigationPress = (e: KeyboardEvent) => {
    if (e.key === down && e.shiftKey && isNavigation) {
      // console.log(down, 'down');
      const isLastElement = elements.length - 1 === currentNavIndex;
      isLastElement ? (currentNavIndex = 0) : (currentNavIndex += 1);

      navigateElement(
        elements,
        true,
        isLastElement ? 'uncolorLast' : undefined
      );
    }

    if (e.key === up && e.shiftKey && isNavigation) {
      // console.log(up, 'up');

      const isFirstElement = currentNavIndex === 0;
      isFirstElement
        ? (currentNavIndex = elements.length - 1)
        : (currentNavIndex -= 1);

      navigateElement(
        elements,
        false,
        isFirstElement ? 'uncolorFirst' : undefined
      );
    }
  };

  /// DO NOT INITIATE TWICE THE EVENT LISTENER WHEN POPUP MODIFICATIONS HAPPEN
  if (isMessagingTrigger) {
    if (!isNavigation) {
      removeColor(elements[currentNavIndex]);
      currentNavIndex = 0;
    } else {
      colorCurrentElementOnMessage(elements, currentNavIndex);
    }
  }

  // IF THE BUTTONS ARE CHANGED IN THE POPUP, DESTROY PREVIOUS CONTROLLER, INITIALIZE NEW LISTENER
  if (
    navigationState?.assignedButtons?.downButtonKey !== down ||
    navigationState?.assignedButtons?.upButtonKey !== up
  ) {
    // console.log('re INIT navbuttons');

    onButtonsChanged(globalSettings, navigationPress);
    return;
  }

  if (isNavigation && !navigationListener) {
    generateNavigationCss();
    navigationListener = new AbortController();

    document.addEventListener('keyup', navigationPress, navigationListener);
    // console.log('adding nav listener');

    (elements[0] as HTMLElement).classList.add(navigationItemClass);
  } else if (!isNavigation && navigationListener?.abort) {
    navigationListener.abort();
    navigationListener = null as unknown as AbortController;

    // console.log('abort nav listener');
  }
}

function navigateElement(
  elements: HTMLElement[],
  isDown?: boolean,
  firstOrLast?: 'uncolorLast' | 'uncolorFirst'
) {
  const currentElement = elements[currentNavIndex] as HTMLElement;

  if (!currentElement) return;

  const previousElement = isDown
    ? (elements[currentNavIndex - 1] as HTMLElement)
    : (elements[currentNavIndex + 1] as HTMLElement);

  currentElement.classList.add(navigationItemClass);

  if (previousElement) {
    removeColor(previousElement);
  }

  if (firstOrLast === 'uncolorFirst') {
    removeColor(elements[0]);
  }
  if (firstOrLast === 'uncolorLast') removeColor(elements[elements.length - 1]);

  currentElement.scrollIntoView({
    behavior: 'smooth',
    block: 'center',
    inline: 'center',
  });
}

function filterHiddenElements(item: QueryItem) {
  // PAGE LIKE LINKEDIN HAVE HIDDEN ELEMENTS

  if (item?.id !== currentWordId) {
    currentNavIndex = 0;
    currentWordId = item?.id ?? '';
  }

  const foundElements = item?.id
    ? document.querySelectorAll(`[${dataHility}='${item?.id}']`)
    : document.getElementsByTagName(hilityTag);

  // console.log(foundElements, 'foundElements');

  const elements = Array.from(foundElements)?.filter(
    (element: any) =>
      element?.checkVisibility
        ? element?.checkVisibility({
            checkOpacity: true, // Check CSS opacity property too
            checkVisibilityCSS: true, // Check CSS visibility property too
          })
        : true // if check visibility doesnt exist, dont filter the hidden elements
  ) as unknown as HTMLElement[];

  // elements.map((hilityItem, i) =>
  //   !hilityItem.id.includes('hility-nav')
  //     ? (hilityItem.id = `hility-nav-${i.toString()}`)
  //     : hilityItem
  // );

  return elements;
}

function colorCurrentElementOnMessage(
  elements: HTMLElement[],
  currentNavIndex: number
) {
  const currentElement = elements[currentNavIndex] as HTMLElement;

  currentElement.classList.add(navigationItemClass);
}
function removeColor(element: HTMLElement) {
  element.classList.remove(navigationItemClass);
}

function setNavigationState(navigationBar: NavigationBar) {
  navigationState = navigationBar;
}

function onButtonsChanged(
  globalSettings: GlobalSettings,
  navigationPress: (e: KeyboardEvent) => void
) {
  navigationListener?.abort();
  navigationListener = null as unknown as AbortController;
  navigationListener = new AbortController();

  document.addEventListener('keyup', navigationPress, navigationListener);
  setNavigationState(globalSettings.navigationBar);

  // console.log('updated buttons');
}

function generateNavigationCss() {
  const doesExist = document.getElementById(navigationItemCssId);

  if (doesExist) return;

  const addedStyle = `
  .${navigationItemClass} {
  background-color: red !important; 
}`;

  const stylesheet = document.createElement('style');
  stylesheet.setAttribute('type', 'text/css');
  stylesheet.setAttribute('id', navigationItemCssId);
  stylesheet.textContent = addedStyle;

  document.head.appendChild(stylesheet);
}

export function disableNavigationCss(isOff: boolean) {
  const navigationCss = document.getElementById(navigationItemCssId);
  // console.log('disable');
  (navigationCss as any).disabled = isOff;
}
