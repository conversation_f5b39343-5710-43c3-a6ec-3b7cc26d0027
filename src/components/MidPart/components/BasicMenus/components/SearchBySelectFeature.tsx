import { <PERSON>lex, FormLabel, Kbd, Switch, Tooltip } from '@chakra-ui/react';
import useStateManager from '../../../../../store/stateManager.hook';
import { kbdStyle, tooltipStyle } from '../../../../../styles/global.styles';
import { disablePopupVisualFeatures } from '../../../../shared/utils/popupStateInterceptor';
import MenuDropdownItem from '../../MenuDropdownItem';

function SearchBySelectFeature({ isDisabled }: { isDisabled?: boolean }) {
  const {
    globalSettings: {
      searchBySelect: { isBright, isOn },
      onSetIsSearchBySelect,
      onSetIsSearchBySelectIsBright,
    },
  } = useStateManager();

  return (
    <MenuDropdownItem
      onYes={() => onSetIsSearchBySelect(true)}
      onNo={() => onSetIsSearchBySelect(false)}
      isYes={disablePopupVisualFeatures(isDisabled, isOn)}
      isMore
      isFeatureDisabled={isDisabled}
      label="Highlight on select"
      labelTooltip="Add to query items the selected phrase with a random color by pressing Shift + L"
    >
      <Flex flexDir={'column'} w="100%" justify={'center'} align="center">
        <Flex px="2" w="100%" justify="space-between">
          <FormLabel fontSize={'11px'} fontWeight="400">
            Bright highlight color:
          </FormLabel>
          <Switch
            onChange={() => onSetIsSearchBySelectIsBright(!isBright)}
            isChecked={isBright}
            size={'sm'}
          />
        </Flex>
        <Tooltip {...tooltipStyle} label="Deselect queries">
          <Kbd {...kbdStyle}>Shift + L</Kbd>
        </Tooltip>
      </Flex>
    </MenuDropdownItem>
  );
}

export default SearchBySelectFeature;
