import { GlobalSettings } from "../../definitions/global.definition";
import { setStorageDataLocal } from "../chrome.utils";
import { invokeBehaviour } from "../content.utils";
import {
  currentGlobalSettings,
  setGlobalSettings,
} from "./content.globalSettingsState";
let selectListener = null as unknown as AbortController;

export const switchOffHotkey = () => {
  const isSwitchHotkeyOn = currentGlobalSettings?.switchOffHotkey?.isOn;

  const selectPress = async (e: KeyboardEvent) => {
    if (e.key === "T" && isSwitchHotkeyOn) {
      const isExtensionOn = !currentGlobalSettings.isExtensionOn;

      const newGlobalSettings = {
        ...currentGlobalSettings,
        isExtensionOn,
      };

      invokeBehaviour(newGlobalSettings, true);

      saveToDatabase(newGlobalSettings);
      setGlobalSettings(newGlobalSettings);
    }
  };

  if (isSwitchHotkeyOn && !selectListener) {
    selectListener = new AbortController();

    document.addEventListener("keydown", selectPress, selectListener);
  } else if (!isSwitchHotkeyOn && selectListener?.abort) {
    selectListener.abort();
    selectListener = null as unknown as AbortController;

    // console.log('abort nav listener');
  }
};

const saveToDatabase = async (newGlobalSettings: GlobalSettings) => {
  const data = {
    globalSettings: newGlobalSettings,
  };

  // setStorageData<GlobalSettings>(data);
  setStorageDataLocal<GlobalSettings>(data);
};
