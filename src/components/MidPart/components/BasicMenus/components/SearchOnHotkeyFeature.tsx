import { Flex, Kbd, Tooltip } from "@chakra-ui/react";
import useStateManager from "../../../../../store/stateManager.hook";
import { kbdStyle, tooltipStyle } from "../../../../../styles/global.styles";
import { disablePopupVisualFeatures } from "../../../../shared/utils/popupStateInterceptor";
import MenuDropdownItem from "../../MenuDropdownItem";

function SearchOnHotkeyFeature({ isDisabled }: { isDisabled?: boolean }) {
  const {
    globalSettings: {
      isSeachOnHotkey: { isOn },
      onSetIsSearchOnHotkey,
    },
  } = useStateManager();

  return (
    <MenuDropdownItem
      onYes={() => onSetIsSearchOnHotkey(true)}
      onNo={() => onSetIsSearchOnHotkey(false)}
      isYes={disablePopupVisualFeatures(isDisabled, isOn)}
      isMore
      isFeatureDisabled={isDisabled}
      label="Manual search mode"
      labelTooltip="Search runs only on Shift + F press, disables automatic search while navigating through webpages, when adding a new query to the list, or when changing query lists."
    >
      <Flex flexDir={"column"} w="100%" justify={"center"} align="center">
        <Tooltip {...tooltipStyle} label="Manually trigger search function">
          <Kbd {...kbdStyle}>Shift + F</Kbd>
        </Tooltip>
      </Flex>
    </MenuDropdownItem>
  );
}

export default SearchOnHotkeyFeature;
