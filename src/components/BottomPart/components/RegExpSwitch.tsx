import {
  Flex,
  FormLabel,
  Icon,
  InputGroup,
  Switch,
  Tooltip,
} from '@chakra-ui/react';
import { Dispatch } from 'react';
import { BsQuestionCircle } from 'react-icons/bs';
import { QueryItem } from '../../../definitions/global.definition';
import useStateManager from '../../../store/stateManager.hook';
import { tooltipStyle } from '../../../styles/global.styles';
import ExclamationTooltip from '../../shared/components/ExclamationTooltip/ExclamationTooltip';
import { FREE_REGEXP_LENGTH } from './QueryAdder';

interface Props {
  newItem: QueryItem;
  setNewItem: Dispatch<React.SetStateAction<QueryItem>>;
}

function RegExpSwitch({ newItem, setNewItem }: Props) {
  const {
    subscription: { subscription },
  } = useStateManager();

  function onToggleIsRegexp() {
    setNewItem((item) => ({ ...item, isRegexp: !item.isRegexp }));
  }

  return (
    <InputGroup
      w="255px"
      display="flex"
      alignContent="center"
      justifyContent="space-evenly"
      onClick={onToggleIsRegexp}
      cursor="pointer"
      _hover={{ bg: 'brand.purpleHover' }}
      transition="all 200ms"
      rounded="md"
      mb="1"
      px="1"
      py="2px"
    >
      <FormLabel
        my="auto"
        mr="auto"
        pos="relative"
        display="flex"
        fontSize={'12px'}
      >
        Is RegExp{' '}
        {!subscription?.isActive && newItem.isRegexp
          ? `Characters left: ${FREE_REGEXP_LENGTH - newItem.name.length}`
          : ''}
        {subscription?.isActive ? null : (
          <Tooltip
            w="190px"
            bg={'brand.purpleFull'}
            label={`More than ${FREE_REGEXP_LENGTH} characters are a 🔐PRO feature.`}
            {...tooltipStyle}
          >
            <Flex align="center" as="span" my="auto" h="18px">
              <Icon
                ml="1"
                cursor="pointer"
                fontSize={'11px'}
                as={BsQuestionCircle}
              />
            </Flex>
          </Tooltip>
        )}
        <ExclamationTooltip />
      </FormLabel>
      <Switch
        ml="0"
        onChange={onToggleIsRegexp}
        my="auto"
        size="sm"
        isChecked={newItem.isRegexp}
      />
    </InputGroup>
  );
}

export default RegExpSwitch;
