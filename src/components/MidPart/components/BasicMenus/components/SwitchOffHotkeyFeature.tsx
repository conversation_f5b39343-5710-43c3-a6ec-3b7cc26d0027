import { Flex, Kbd, Tooltip } from '@chakra-ui/react';
import useStateManager from '../../../../../store/stateManager.hook';
import { kbdStyle, tooltipStyle } from '../../../../../styles/global.styles';
import { disablePopupVisualFeatures } from '../../../../shared/utils/popupStateInterceptor';
import MenuDropdownItem from '../../MenuDropdownItem';

function SwitchOffHotkeyFeature({ isDisabled }: { isDisabled?: boolean }) {
  const { globalSettings } = useStateManager();

  return (
    <MenuDropdownItem
      onYes={() => globalSettings.onSetIsSwitchOffHotkey(true)}
      onNo={() => globalSettings.onSetIsSwitchOffHotkey(false)}
      isYes={disablePopupVisualFeatures(
        isDisabled,
        globalSettings?.switchOffHotkey?.isOn
      )}
      isMore
      label="Switch off hotkey"
      labelTooltip="Turn off the extension by a hotkey while browsing, without opening the extension window."
      isFeatureDisabled={isDisabled}
    >
      <Flex w="100%" justify={'center'} align="center">
        <Tooltip {...tooltipStyle} label="Extension turn off keys">
          <Kbd {...kbdStyle}>Shift + T</Kbd>
        </Tooltip>
      </Flex>
    </MenuDropdownItem>
  );
}

export default SwitchOffHotkeyFeature;
