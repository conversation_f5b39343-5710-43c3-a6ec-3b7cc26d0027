import { <PERSON>ton, <PERSON>ade, Flex, Icon, Select, Text } from "@chakra-ui/react";
import { useEffect, useState } from "react";
import { BsTypeUnderline } from "react-icons/bs";
import { getThicknessValue } from "../../../chrome/features/utils/decoration.utils";
import { QueryItem } from "../../../definitions/global.definition";
import useStateManager from "../../../store/stateManager.hook";
import { menuButtonStyle } from "../../../styles/global.styles";
import { adaptiveFontColor } from "../../../components/shared/utils/color.utils";

interface Props {
  newItem: QueryItem;
  onQueryItemDecorationType: (type: string) => void;
  onQueryItemDecorationThickness: (thickness: string) => void;
  isEdit?: boolean; // Flag to indicate if we're editing an existing item
}

// Decoration type options
const decorationTypes = [
  { value: "background", label: "Background" },
  { value: "underline", label: "Underline" },
  { value: "overline", label: "Overline" },
  { value: "line-through", label: "Line Through" },
  { value: "dotted-underline", label: "Dotted Underline" },
  { value: "wavy-underline", label: "Wavy Underline" },
  { value: "double-underline", label: "Double Underline" },
];

// Thickness options
const thicknessOptions = [
  { value: "thin", label: "Thin" },
  { value: "medium", label: "Medium" },
  { value: "thick", label: "Thick" },
  { value: "very-thick", label: "Very Thick" },
  { value: "extra-thick", label: "Extra Thick" },
  { value: "auto", label: "Auto" },
];

/**
 * Helper function to generate decoration style for preview
 */
const getDecorationPreviewStyle = (item: QueryItem): React.CSSProperties => {
  if (!item.decorationType || item.decorationType === "background") {
    return {
      backgroundColor: `rgba(${item.colors.rgb.r}, ${item.colors.rgb.g}, ${item.colors.rgb.b}, ${item.colors.rgb.a})`,
      color: adaptiveFontColor(item.colors.hsl, true),
      padding: "0 4px",
      borderRadius: "4px",
    };
  }

  // Get the color string
  const colorString = `rgba(${item.colors.rgb.r}, ${item.colors.rgb.g}, ${item.colors.rgb.b}, ${item.colors.rgb.a})`;

  // Get thickness value using the utility function
  const thicknessValue = getThicknessValue(item.decorationThickness);

  // For extra-thick and very-thick, we'll add additional styling to make it more visible
  const isExtraThick =
    item.decorationThickness === "extra-thick" ||
    item.decorationThickness === "very-thick";

  // Create base style with color, padding and margin
  const baseStyle: React.CSSProperties = {
    // Set text color based on decoration type
    color: "black",
    // Set display to inline-block to ensure decorations are properly applied
    display: "inline-block",
    // Add padding to make decorations more visible
    padding: "4px 2px",
    // For extra thick decorations, add more space
    paddingBottom: isExtraThick ? "8px" : "4px",
  };

  // Add specific decoration type properties
  switch (item.decorationType) {
    case "underline":
      return {
        ...baseStyle,
        textDecorationLine: "underline",
        textDecorationColor: colorString,
        textDecorationThickness: thicknessValue,
      };
    case "overline":
      return {
        ...baseStyle,
        textDecorationLine: "overline",
        textDecorationColor: colorString,
        textDecorationThickness: thicknessValue,
      };
    case "line-through":
      return {
        ...baseStyle,
        textDecorationLine: "line-through",
        textDecorationColor: colorString,
        textDecorationThickness: thicknessValue,
      };
    case "dotted-underline":
      return {
        ...baseStyle,
        textDecorationLine: "underline",
        textDecorationStyle: "dotted",
        textDecorationColor: colorString,
        textDecorationThickness: thicknessValue,
      };
    case "wavy-underline":
      return {
        ...baseStyle,
        textDecorationLine: "underline",
        textDecorationStyle: "wavy",
        textDecorationColor: colorString,
        textDecorationThickness: thicknessValue,
      };
    case "double-underline":
      return {
        ...baseStyle,
        textDecorationLine: "underline",
        textDecorationStyle: "double",
        textDecorationColor: colorString,
        textDecorationThickness: thicknessValue,
      };
    default:
      return baseStyle;
  }
};

function QueryDecorationInput({
  newItem,
  onQueryItemDecorationType,
  onQueryItemDecorationThickness,
  isEdit = false,
}: Props) {
  const {
    accessibility,
    globalSettings: { onSetQueryItemDecoration },
  } = useStateManager();

  // Create local state to track decoration settings
  const [decorationSettings, setDecorationSettings] = useState<{
    type: QueryItem["decorationType"];
    thickness: QueryItem["decorationThickness"];
  }>({
    type: newItem.decorationType || "background",
    thickness: newItem.decorationThickness || "medium",
  });

  // Update local state when props change
  useEffect(() => {
    setDecorationSettings({
      type: newItem.decorationType || "background",
      thickness: newItem.decorationThickness || "medium",
    });
  }, [newItem.decorationType, newItem.decorationThickness]);

  // Toggle decoration settings visibility
  const toggleDecorationSettings = () => {
    accessibility.onToggleAccessibilitySettings();
  };

  if (!newItem?.name?.length) return null;

  return (
    <Fade in={newItem?.name?.length > 0} style={{ transition: "all 150ms" }}>
      <Flex
        rounded="md"
        borderColor="brand.purple"
        bg={"rgb(255, 255, 255, 70%)"}
        py="2"
        px="1"
        flexDir={"column"}
        w="240px"
        mx="auto"
        borderTopRadius={"unset"}
        shadow="sm"
        mt={2}
      >
        <Flex w="100%" justify={"space-between"} mb={2}>
          <Button
            {...menuButtonStyle}
            onClick={toggleDecorationSettings}
            mx="auto"
          >
            <Flex align="center">
              <Text fontWeight={"500"} mr="2px" fontSize="10px">
                Text Decoration
              </Text>
              <Icon as={BsTypeUnderline} ml={1} fontSize="14px" />
            </Flex>
          </Button>
        </Flex>

        {accessibility.isAccessibilitySettings && (
          <Flex direction="column" gap={2}>
            <Flex direction="column">
              <Text fontSize="xs" mb={1}>
                Decoration Type
              </Text>
              <Select
                size="sm"
                fontSize="12px"
                bg="white"
                padding="3px"
                borderRadius="md"
                border="2px solid transparent"
                shadow="0px 0px 0px 1px white"
                _hover={{
                  border:
                    "2px solid var(--chakra-colors-brand-inputPurpleHover)",
                  outline: "transparent",
                }}
                _focus={{
                  outline: "brand.inputPurpleHover",
                  border:
                    "2px solid var(--chakra-colors-brand-inputPurpleHover)",
                  boxShadow: "0 0 2px #719ece",
                }}
                value={decorationSettings.type || "background"}
                onChange={(e) => {
                  const newType = e.target.value as QueryItem["decorationType"];

                  // Update local state immediately
                  setDecorationSettings((prev) => ({
                    ...prev,
                    type: newType,
                    // Reset thickness to undefined if background
                    thickness:
                      newType === "background"
                        ? undefined
                        : prev.thickness || "medium",
                  }));

                  // Update parent component state
                  onQueryItemDecorationType(newType || "background");

                  // If editing an existing item, also update it in the global state
                  if (isEdit && newItem.id) {
                    // Preserve the current thickness when changing decoration type
                    onSetQueryItemDecoration(
                      newItem.id,
                      newType || "background",
                      newType !== "background"
                        ? decorationSettings.thickness
                        : undefined
                    );
                  }
                }}
              >
                {decorationTypes.map((type) => (
                  <option
                    key={type.value}
                    value={type.value}
                    style={{ fontSize: "12px" }}
                  >
                    {type.label}
                  </option>
                ))}
              </Select>
            </Flex>

            {/* Only show thickness if not using background */}
            {decorationSettings.type &&
              decorationSettings.type !== "background" && (
                <Flex direction="column">
                  <Text fontSize="xs" mb={1}>
                    Thickness
                  </Text>
                  <Select
                    size="sm"
                    fontSize="12px"
                    bg="white"
                    padding="3px"
                    borderRadius="md"
                    border="2px solid transparent"
                    shadow="0px 0px 0px 1px white"
                    _hover={{
                      border:
                        "2px solid var(--chakra-colors-brand-inputPurpleHover)",
                      outline: "transparent",
                    }}
                    _focus={{
                      outline: "brand.inputPurpleHover",
                      border:
                        "2px solid var(--chakra-colors-brand-inputPurpleHover)",
                      boxShadow: "0 0 2px #719ece",
                    }}
                    value={decorationSettings.thickness || "medium"}
                    onChange={(e) => {
                      const newThickness = e.target
                        .value as QueryItem["decorationThickness"];
                      // Update local state immediately
                      setDecorationSettings((prev) => ({
                        ...prev,
                        thickness: newThickness,
                      }));

                      // Update parent component state
                      onQueryItemDecorationThickness(newThickness || "medium");

                      // If editing an existing item, also update it in the global state
                      if (isEdit && newItem.id) {
                        onSetQueryItemDecoration(
                          newItem.id,
                          decorationSettings.type || "underline",
                          newThickness
                        );
                      }
                    }}
                  >
                    {thicknessOptions.map((option) => (
                      <option
                        key={option.value}
                        value={option.value}
                        style={{ fontSize: "12px" }}
                      >
                        {option.label}
                      </option>
                    ))}
                  </Select>
                </Flex>
              )}

            {/* Preview of the decoration */}
            <Flex direction="column" bg="white" px={2} borderRadius="md">
              <Flex
                borderRadius="md"
                bg="gray.50"
                justifyContent="center"
                alignItems="center"
              >
                <Text
                  fontWeight="medium"
                  style={getDecorationPreviewStyle({
                    ...newItem,
                    decorationType: decorationSettings.type,
                    decorationThickness: decorationSettings.thickness,
                  })}
                  fontSize={"12px"}
                >
                  {newItem.name}
                </Text>
              </Flex>
            </Flex>
          </Flex>
        )}
      </Flex>
    </Fade>
  );
}

export default QueryDecorationInput;
