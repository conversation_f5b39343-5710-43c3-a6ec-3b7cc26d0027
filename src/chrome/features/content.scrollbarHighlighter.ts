import { GlobalSettings } from "../../definitions/global.definition";
import { hilityTag, dataHility } from "../content.consts";
import { debounce } from "../shared";

// Define a type for the marker
type Marker = {
  element: HTMLElement;
  markerEl: HTMLElement;
};

// Track state
let highlighterEl: HTMLElement | null = null;
let markers: Marker[] = [];
let resizeObserver: ResizeObserver | null = null;
let scrollHandler: (() => void) | null = null;
let isEnabled = false;

// Create the highlighter element
const createHighlighterElement = (): void => {
  // Remove existing highlighter if any
  if (highlighterEl) {
    highlighterEl.remove();
  }

  // Create new highlighter element
  highlighterEl = document.createElement('div');
  highlighterEl.className = 'highlighty-scrollbar-highlighter';

  // Style the highlighter
  Object.assign(highlighterEl.style, {
    position: 'fixed',
    right: '0',
    top: '0',
    width: '8px',
    height: '100%',
    pointerEvents: 'auto',
    zIndex: '9999',
    backgroundColor: 'transparent'
  });

  // Append to body
  document.body.appendChild(highlighterEl);

  // Add CSS for markers if not already added
  if (!document.getElementById('highlighty-scrollbar-style')) {
    const style = document.createElement('style');
    style.id = 'highlighty-scrollbar-style';
    style.textContent = `
      .highlighty-scrollbar-marker {
        position: absolute;
        width: 100%;
        height: 5px;
        background-color: rgba(255, 204, 0, 0.7);
        border-radius: 2px;
        cursor: pointer;
        transition: opacity 0.2s ease, width 0.2s ease, transform 0.2s ease;
        /* Add a subtle border to ensure visibility for light colors */
        box-shadow: 0 0 1px rgba(0, 0, 0, 0.5);
      }
      .highlighty-scrollbar-marker:hover {
        opacity: 1;
        width: 12px;
        transform: translateX(-2px);
      }
    `;
    document.head.appendChild(style);
  }
};

// Set up event listeners
const setupEventListeners = (): void => {
  // Handle scrolling
  scrollHandler = debounce(() => updateMarkerVisibility(), 100);
  window.addEventListener('scroll', scrollHandler);

  // Handle resizing
  resizeObserver = new ResizeObserver(
    debounce(() => updateMarkerPositions(), 100)
  );
  resizeObserver.observe(document.body);

  // Handle clicks on the highlighter
  if (highlighterEl) {
    highlighterEl.addEventListener('click', handleHighlighterClick);
  }
};

// Create a marker for a highlighted element
const createMarker = (element: HTMLElement, index: number): void => {
  if (!highlighterEl) return;

  const marker = document.createElement('div');
  marker.className = 'highlighty-scrollbar-marker';
  marker.dataset.index = index.toString();

  // Get the color from the highlighted element if possible
  let markerColor = 'rgba(255, 204, 0, 0.7)'; // Default color
  const hilityId = element.getAttribute(dataHility);

  if (hilityId) {
    // Check if this element uses text decoration
    const decorationType = element.getAttribute('data-decoration-type');
    const styleElement = document.getElementById(hilityId);

    if (styleElement && styleElement.textContent) {
      // If it's a text decoration, look for the decoration color
      if (decorationType && decorationType !== 'background') {
        // Try to get the text-decoration-color from the style element
        const decorColorMatch = styleElement.textContent.match(/text-decoration-color:\s*([^;!]+)/);
        if (decorColorMatch && decorColorMatch[1]) {
          markerColor = decorColorMatch[1].trim();
        } else {
          // Fallback: try to get the color from the element's computed style
          const computedStyle = window.getComputedStyle(element);
          const decorColor = computedStyle.getPropertyValue('text-decoration-color');
          if (decorColor && decorColor !== 'currentcolor') {
            markerColor = decorColor;
          }
        }
      } else {
        // For background highlights, use the background color
        const bgMatch = styleElement.textContent.match(/background:\s*([^;!]+)/);
        if (bgMatch && bgMatch[1]) {
          markerColor = bgMatch[1].trim();
        }
      }
    }
  }

  // Style the marker
  Object.assign(marker.style, {
    backgroundColor: markerColor,
    opacity: '0.7'
  });

  highlighterEl.appendChild(marker);
  markers.push({
    element: element,
    markerEl: marker
  });
};

// Update marker positions based on highlighted elements
const updateMarkerPositions = (): void => {
  if (!isEnabled || markers.length === 0) return;

  const documentHeight = Math.max(
    document.body.scrollHeight,
    document.documentElement.scrollHeight
  );

  markers.forEach(marker => {
    const elementRect = marker.element.getBoundingClientRect();
    const elementTop = elementRect.top + window.scrollY;

    // Calculate the relative position of the element within the document
    const relativePosition = elementTop / documentHeight;

    // Position the marker
    marker.markerEl.style.top = `${relativePosition * 100}%`;
  });
};

// Update marker visibility based on scroll position
const updateMarkerVisibility = (): void => {
  if (!isEnabled || markers.length === 0) return;

  const windowTop = window.scrollY;
  const windowBottom = windowTop + window.innerHeight;

  markers.forEach(marker => {
    const elementRect = marker.element.getBoundingClientRect();
    const elementTop = elementRect.top + window.scrollY;
    const elementBottom = elementTop + elementRect.height;

    // Check if the element is currently visible in the viewport
    const isVisible =
      elementBottom > windowTop &&
      elementTop < windowBottom;

    // Highlight the current marker
    marker.markerEl.style.opacity = isVisible ? '1' : '0.7';
    marker.markerEl.style.width = isVisible ? '10px' : '8px';

    // Add a stronger shadow for visible markers to ensure they stand out
    marker.markerEl.style.boxShadow = isVisible
      ? '0 0 2px rgba(0, 0, 0, 0.7), 0 0 3px rgba(255, 255, 255, 0.5)'
      : '0 0 1px rgba(0, 0, 0, 0.5)';
  });
};

/**
 * Handle clicks on the highlighter
 */
const handleHighlighterClick = async (e: MouseEvent): Promise<void> => {
  if (!isEnabled || !highlighterEl || markers.length === 0) return;

  const highlighterRect = highlighterEl.getBoundingClientRect();
  const clickPositionY = (e.clientY - highlighterRect.top) / highlighterRect.height;

  // Find the closest marker to the click position
  let closestIndex = -1;
  let closestDistance = Infinity;

  // Find the index of the closest marker
  for (let i = 0; i < markers.length; i++) {
    const marker = markers[i];
    const markerRect = marker.markerEl.getBoundingClientRect();
    const markerPosition = (markerRect.top - highlighterRect.top) / highlighterRect.height;
    const distance = Math.abs(markerPosition - clickPositionY);

    if (distance < closestDistance) {
      closestDistance = distance;
      closestIndex = i;
    }
  }

  // If we found a marker, scroll to it
  if (closestIndex !== -1) {
    const marker = markers[closestIndex];

    // Scroll to the element
    marker.element.scrollIntoView({
      behavior: 'smooth',
      block: 'center'
    });

    try {
      // Highlight the element temporarily
      await highlightElement(marker.element);
    } catch (error) {
      console.error('Error highlighting element:', error);
    }
  }
};

/**
 * Helper function to create a promise that resolves after a specified delay
 */
const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * Add a temporary highlight effect to an element
 * Using async/await for cleaner animation sequence
 */
const highlightElement = async (element: HTMLElement): Promise<void> => {
  // Store original styles
  const originalTransition = element.style.transition;
  const originalZIndex = element.style.zIndex;
  const originalBoxShadow = element.style.boxShadow;
  const originalTransform = element.style.transform;

  // Create a temporary overlay for text-decorated elements
  const decorationType = element.getAttribute('data-decoration-type');
  let overlay: HTMLDivElement | null = null;

  try {
    // Apply initial highlight effect
    element.style.transition = 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
    element.style.zIndex = '9999';
    element.style.transform = 'scale(1.15)';
    element.style.boxShadow = '0 0 15px 4px rgba(255, 255, 0, 0.8), 0 0 5px 2px rgba(255, 165, 0, 0.6)';

    // Create overlay for text-decorated elements
    if (decorationType && decorationType !== 'background') {
      overlay = document.createElement('div');
      const rect = element.getBoundingClientRect();

      Object.assign(overlay.style, {
        position: 'absolute',
        left: rect.left + 'px',
        top: rect.top + 'px',
        width: rect.width + 'px',
        height: rect.height + 'px',
        backgroundColor: 'rgba(255, 255, 0, 0.25)',
        borderRadius: '3px',
        pointerEvents: 'none',
        zIndex: '9998',
        transition: 'all 0.4s ease',
        opacity: '1',
        boxShadow: '0 0 8px 2px rgba(255, 255, 0, 0.5)',
        border: '1px solid rgba(255, 215, 0, 0.6)'
      });

      document.body.appendChild(overlay);
    }

    // Phase 1: Keep the highlight at full scale for 1 second
    await delay(1000);

    // Phase 2: Scale back to normal size but keep the glow
    element.style.transform = 'scale(1)';

    // Phase 3: Start fading out effects after another second
    await delay(1000);

    if (overlay) {
      overlay.style.opacity = '0';
    }

    // Gradually reduce the glow effect
    element.style.boxShadow = '0 0 5px 1px rgba(255, 255, 0, 0.5)';

    // Phase 4: Remove all effects after 500ms
    await delay(500);
  } finally {
    // Always restore original styles, even if there was an error
    element.style.transition = originalTransition;
    element.style.zIndex = originalZIndex;
    element.style.boxShadow = originalBoxShadow;

    // Only reset transform if it wasn't originally set
    if (!originalTransform) {
      element.style.transform = '';
    } else {
      element.style.transform = originalTransform;
    }

    // Remove overlay if it exists
    if (overlay && overlay.parentNode) {
      overlay.remove();
    }
  }
};

// Clear all markers
export const clearScrollbarMarkers = (): void => {
  markers.forEach(marker => {
    if (marker.markerEl && marker.markerEl.parentNode) {
      marker.markerEl.remove();
    }
  });
  markers = [];
};

// Update markers based on highlighted elements
export const updateScrollbarMarkers = (): void => {
  if (!isEnabled || !highlighterEl) return;

  // Clear existing markers
  clearScrollbarMarkers();

  // Get all highlighted elements
  const highlightedElements = document.getElementsByTagName(hilityTag);
  if (!highlightedElements || highlightedElements.length === 0) return;

  // Create markers for each highlighted element
  Array.from(highlightedElements).forEach((element, index) => {
    createMarker(element as HTMLElement, index);
  });

  // Update marker positions
  updateMarkerPositions();
};

// Initialize the scrollbar highlighter
export const initScrollbarHighlighter = (settings: GlobalSettings): void => {
  // Check if the feature is enabled in settings
  if (!settings.scrollbarHighlighter?.isOn) {
    destroyScrollbarHighlighter();
    return;
  }

  if (isEnabled) return;

  createHighlighterElement();
  setupEventListeners();
  isEnabled = true;

  // Initial update of markers
  updateScrollbarMarkers();
};

// Destroy the scrollbar highlighter
export const destroyScrollbarHighlighter = (): void => {
  // Remove event listeners
  if (scrollHandler) {
    window.removeEventListener('scroll', scrollHandler);
    scrollHandler = null;
  }

  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }

  // Remove DOM elements
  clearScrollbarMarkers();
  if (highlighterEl) {
    highlighterEl.remove();
    highlighterEl = null;
  }

  isEnabled = false;
};

// Toggle the scrollbar highlighter
export const toggleScrollbarHighlighter = (enabled: boolean): void => {
  if (enabled && !isEnabled) {
    createHighlighterElement();
    setupEventListeners();
    isEnabled = true;
    updateScrollbarMarkers();
  } else if (!enabled && isEnabled) {
    destroyScrollbarHighlighter();
  }
};
