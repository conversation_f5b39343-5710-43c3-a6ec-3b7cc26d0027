import { AnalyticsData } from "../../../../chrome/features/content.analytics";
import { BROWSER_CALLER } from "../../../../chrome/shared";
import { ChromeMessage, Sender } from "../../../../types";

export function validateSender(
  message: ChromeMessage,
  sender: chrome.runtime.MessageSender
) {
  //   console.log(message, 'content MESSAGE');
  return (
    BROWSER_CALLER?.runtime?.id &&
    sender?.id === BROWSER_CALLER?.runtime?.id &&
    message.from === Sender.Analytics
  );
}

export function receiveMessageFromContent(
  setAnalyticsData: React.Dispatch<React.SetStateAction<AnalyticsData[]>>
) {
  BROWSER_CALLER?.runtime?.onMessage &&
    BROWSER_CALLER.runtime.onMessage.addListener((message, sender) => {
      if (validateSender(message, sender)) {
        setAnalyticsData(message.message.analyticsData);
      }
    });
}

export function exportCSV(analyticData: AnalyticsData[]) {
  const csvContent = convertArrayOfJSONToCSV(analyticData);

  const blob = new Blob([csvContent], { type: "text/csv" });

  const url = window.URL.createObjectURL(blob);

  const downloadLink = document.createElement("a");
  downloadLink.href = url;
  downloadLink.download = `highlighty-analytics-${new Date().getDate()}-${new Date().getMonth()}-${new Date().getFullYear()}.csv`;

  downloadLink.click();

  window.URL.revokeObjectURL(url);
}

function convertArrayOfJSONToCSV(analyticData: AnalyticsData[]) {
  let csv = "";

  const header = ["Query", "URL", "Date", "Found amount"];

  csv += header.join(",") + "\n";

  analyticData.forEach(({ data, url, visitTime }) => {
    data.forEach(({ queryItem, collectedData }) => {
      const rowData = [
        queryItem.name,
        url,
        getDate(visitTime),
        collectedData?.number ?? 0,
      ];
      csv += rowData.join(",") + "\n";
    });
  });

  return csv;
}

function getDate(dateString: string) {
  // Split the date and time components
  const [datePart, timePart] = dateString.split(", ");

  // Extract date components (day, month, year)
  const [day, month, year] = datePart.split("/").map(Number);

  // Extract time components (hour, minute)
  const [hour, minute] = timePart.split(":").map(Number);

  // Format the date components
  const formattedDate = `${year}-${month < 10 ? "0" + month : month}-${
    day < 10 ? "0" + day : day
  } ${hour < 10 ? "0" + hour : hour}:${minute < 10 ? "0" + minute : minute}`;

  return formattedDate;
}
