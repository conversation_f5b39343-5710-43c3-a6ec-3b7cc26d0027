import { Skeleton } from "@chakra-ui/react";
import BottomPart from "../BottomPart/BottomPart";
import MidPart from "../MidPart/MidPart";
import TopPart from "../TopPart/TopPart";
import useInitAccountSubscription from "../shared/hooks/initAccountSubscription.hook";
import useInitSettingView from "../shared/hooks/initSettingView.hook";

import MainWrapper from "./components/MainWrapper";
import SkeletonLoader from "./components/SkeletonLoader";
import { SupportEmailText } from "./components/SupportEmailText";

function App() {
  const { isViewLoading } = useInitSettingView();

  useInitAccountSubscription();

  return (
    <MainWrapper>
      <TopPart />
      {isViewLoading ? (
        <SkeletonLoader />
      ) : (
        <>
          <MidPart />
          <BottomPart />
        </>
      )}
      <SupportEmailText />
    </MainWrapper>
  );
}

export default App;
