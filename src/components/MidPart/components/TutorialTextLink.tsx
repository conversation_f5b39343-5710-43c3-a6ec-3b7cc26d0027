import { Text } from "@chakra-ui/react";
import { HIGHLIGHTY_DOMAIN } from "../../shared/utils/constants.consts";

function TutorialTextLink() {
  return (
    <Text
      color="brand.textColor"
      textAlign="center"
      fontSize="12px"
      textDecor={"none"}
      opacity="0.75"
      my="1"
    >
      If you feel lost, check our tutorials
      <a
        rel="noreferrer"
        href={`${HIGHLIGHTY_DOMAIN}/how-to-use`}
        target="_blank"
      >
        <Text as="b" color="#007bff">
          {" "}
          here
        </Text>
        .
      </a>
    </Text>
  );
}

export default TutorialTextLink;
