import { User } from "firebase/auth";
import { PaddleSubscription } from "../../../../definitions/global.definition";
import { ToastTypesPopup } from "../../../shared/components/Toast/CustomToast.hook";
import { setStorageDataLocal } from "../../../../chrome/chrome.utils";
import { BROWSER_CALLER } from "../../../../chrome/shared";

const newTabTimeout = 0;

export const products = [
  { interval: "monthly", id: process.env.REACT_APP_MONTHLY, price: 369 },
  { interval: "yearly", id: process.env.REACT_APP_YEARLY, price: 2999 },
];

function crypt(value: string) {
  return btoa(value);
}

export async function getPaddleSubscription(
  user: User | null
): Promise<PaddleSubscription | null> {
  if (!user) return null;

  const response = await fetch(
    `${process.env.REACT_APP_FIREBASE_DOMAIN}/getSubscription?uid=${user.uid}`
  ).then((response) => response.json());

  return response;
}

export async function createCheckoutSessionPaddle(
  customToast: (message: string, type: ToastTypesPopup) => void,
  setIsPaddleLoading: React.Dispatch<React.SetStateAction<boolean>>,
  email: string | null,
  priceId?: string,
  uid?: string
) {
  if (!priceId || !email || !uid) return;

  const base = process.env.REACT_APP_WEBSITE;

  const encrypted = crypt(`email=${email}&product=${priceId}&uid=${uid}`);

  const url = base + encrypted;

  setIsPaddleLoading(true);

  if (url) {
    customToast("Once paid, please reopen the extension popup.", "info");

    setTimeout(() => {
      if (BROWSER_CALLER?.tabs?.create) {
        BROWSER_CALLER.tabs.create({ active: true, url: url });
      } else {
        window.open(url, "_blank");
      }
    }, newTabTimeout);
  }
}

export function managePaddleSubscription(url?: string) {
  setTimeout(() => {
    if (BROWSER_CALLER?.tabs?.create) {
      BROWSER_CALLER.tabs.create({ active: true, url: url });
    } else {
      window.open(url, "_blank");
    }
  }, newTabTimeout);
}

export function refreshSubscriptionStorage(result: PaddleSubscription | null) {
  const data = {
    subscription: result,
  };

  setStorageDataLocal<PaddleSubscription | null>(data);
}
