import { GlobalSettings } from "../definitions/global.definition";
import { getStorageDataLocal } from "./chrome.utils";
import { invokeBehaviour, receiveMessageFromPopup } from "./content.utils";
import {
  currentGlobalSettings,
  setGlobalSettings,
} from "./features/content.globalSettingsState";
import { globalSettingsFreemiumConverter } from "./features/content.paygate";

const contentMessageRunner = () =>
  (chrome ?? browser).runtime.onMessage.addListener(receiveMessageFromPopup);

async function onStart() {
  const dataLocal = (await getStorageDataLocal("globalSettings")) as {
    globalSettings: GlobalSettings;
  };

  contentMessageRunner();

  if (!dataLocal?.globalSettings) return;

  const globalSettings = await globalSettingsFreemiumConverter(
    dataLocal.globalSettings
  );

  setGlobalSettings(globalSettings);

  invokeBehaviour(currentGlobalSettings);
}

onStart();
