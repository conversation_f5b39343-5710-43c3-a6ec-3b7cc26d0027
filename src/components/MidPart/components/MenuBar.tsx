import { Flex, Icon, Switch, Text, Tooltip } from "@chakra-ui/react";

import { IoIosArrowForward } from "react-icons/io";
import useStateManager from "../../../store/stateManager.hook";
import { tooltipStyle } from "../../../styles/global.styles";

function MenuBar() {
  const { menuDropdown, globalSettings } = useStateManager();

  return (
    <Flex
      rounded="lg"
      w={"100%"}
      bg={"brand.menuBarBg"}
      shadow="0 1px 2px 0 rgb(0 0 0 / 26%)"
      justify={"space-between"}
      align="center"
      pl="2"
      pr="3"
      mx="auto"
      height={"24px"}
    >
      <Flex
        cursor="pointer"
        userSelect="none"
        justify={"center"}
        align="center"
        mt="-2px"
        onClick={menuDropdown.onToggleMenu}
      >
        <Text fontSize="sm" fontWeight={400} mr="3px">
          settings
        </Text>

        <Icon
          transform={menuDropdown.isMenuOpen ? "rotate(90deg)" : ""}
          transition="all 150ms"
          as={IoIosArrowForward}
          width="12.4px"
          mt="1px"
        />
      </Flex>

      {process.env.REACT_APP_ENV}

      <Tooltip
        zIndex={9999}
        color="white"
        bg="brand.paragraphColor"
        placement="top"
        {...tooltipStyle}
        label={`Turn ${
          globalSettings?.isExtensionOn ? "OFF" : "ON"
        } the extension`}
      >
        <Flex>
          <Switch
            onChange={globalSettings.onIsExtensionOn}
            isChecked={globalSettings?.isExtensionOn}
            size="sm"
          />
        </Flex>
      </Tooltip>
    </Flex>
  );
}

export default MenuBar;
