import type { PayloadAction } from "@reduxjs/toolkit";
import { createSlice } from "@reduxjs/toolkit";
import {
  AccountSubscription,
  UserAccount,
  UserSubscription,
} from "../definitions/global.definition";

const init = { account: null, subscription: null, isLoading: true };

export const accountSubscriptionSlice = createSlice({
  name: "accountSubscriptionSlice",
  initialState: init as AccountSubscription,
  reducers: {
    setAccountAndSubsription: (
      state,
      action: PayloadAction<AccountSubscription>
    ) => {
      state = action.payload;
    },
    setAccount: (state, action: PayloadAction<UserAccount>) => {
      state.account = action.payload;
    },
    setIsLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setSubscription: (state, action: PayloadAction<UserSubscription>) => {
      state.subscription = action.payload;
    },
  },
});

export const {
  setIsLoading,
  setAccountAndSubsription,
  setAccount,
  setSubscription,
} = accountSubscriptionSlice.actions;

export default accountSubscriptionSlice.reducer;
