import { Flex } from "@chakra-ui/react";
import PremiumTooltip from "../../../shared/components/PremiumTooltip/PremiumTooltip";
import FeatureGroupingAccordion from "../FeatureGroupingAccordion";
import InputFocusFeature from "./components/InputFocusFeature";
import IsCompactViewFeature from "./components/IsCompactViewFeature";
import MinCharLimitFeature from "./components/MinCharLimitFeature";
import NavigationBarFeature from "./components/NavigationBarFeature/NavigationBarFeature";
import QueryClickInvertedFeature from "./components/QueryClickInvertedFeature";
import QueryFoundFeature from "./components/QueryFoundFeature";
import QueryNotFoundFeature from "./components/QueryNotFoundFeature";
import ScrollbarHighlighterFeature from "./components/ScrollbarHighlighterFeature";
import SearchBySelectFeature from "./components/SearchBySelectFeature";
import SwitchOffHotkeyFeature from "./components/SwitchOffHotkeyFeature";
import TotalFoundBadgeFeature from "./components/TotalFoundBadgeFeature";
import TemporaryDeselectFeature from "../AdvancedMenus/components/TemporaryDeselectFeature";
import DisableQueryListClicksFeature from "./components/DisableQueryListClicksFeature";
import SearchOnHotkeyFeature from "./components/SearchOnHotkeyFeature";

function BasicMenus() {
  const isChrome = process.env.REACT_APP_BROWSER_TYPE === "chrome";
  return (
    <Flex flexDir={"column"}>
      {/* <ColorModeFeature /> */}

      <FeatureGroupingAccordion title="User-interface settings">
        <IsCompactViewFeature />

        <QueryClickInvertedFeature />

        <DisableQueryListClicksFeature />

        <MinCharLimitFeature />
        <InputFocusFeature />
      </FeatureGroupingAccordion>

      <FeatureGroupingAccordion title="Notification settings">
        <ScrollbarHighlighterFeature />
        <QueryFoundFeature />
        <QueryNotFoundFeature />
        {isChrome ? (
          <PremiumTooltip>
            <TotalFoundBadgeFeature />
          </PremiumTooltip>
        ) : (
          <></>
        )}
      </FeatureGroupingAccordion>

      <FeatureGroupingAccordion title="Hotkey features">
        <SearchOnHotkeyFeature />

        <PremiumTooltip>
          <SwitchOffHotkeyFeature />
        </PremiumTooltip>

        <PremiumTooltip>
          <SearchBySelectFeature />
        </PremiumTooltip>

        <NavigationBarFeature />

        <PremiumTooltip>
          <TemporaryDeselectFeature />
        </PremiumTooltip>
      </FeatureGroupingAccordion>
    </Flex>
  );
}

export default BasicMenus;
