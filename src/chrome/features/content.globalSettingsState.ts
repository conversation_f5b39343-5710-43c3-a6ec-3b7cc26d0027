import { GlobalSettings } from '../../definitions/global.definition';

export let currentGlobalSettings: GlobalSettings;

export const hasGlobalSettingsChanged = (globalSettings: GlobalSettings) => {
  if (!globalSettings) return false;

  if (
    JSON.stringify(currentGlobalSettings) === JSON.stringify(globalSettings)
  ) {
    // console.log('globalSettings have not changed');
    return false;
  } else {
    currentGlobalSettings = globalSettings;
    // console.log('globalSettings have changed');
    return true;
  }
};

export const setGlobalSettings = (globalSettings: GlobalSettings) =>
  (currentGlobalSettings = globalSettings);
