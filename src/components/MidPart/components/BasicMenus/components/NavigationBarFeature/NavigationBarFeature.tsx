import { Button, Flex, Kbd, Text, Tooltip } from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import useStateManager from '../../../../../../store/stateManager.hook';
import {
  kbdStyle,
  menuButtonStyle,
  tooltipStyle
} from '../../../../../../styles/global.styles';
import MenuDropdownItem from '../../../MenuDropdownItem';

function NavigationBarFeature() {
  const { globalSettings } = useStateManager();
  const assignedButtons = globalSettings.navigationBar.assignedButtons;
  const [isKeySetting, setIsKeySetting] = useState(false);
  const [isKeyDownFocused, setIsKeyDownFocused] = useState(false);

  const [isDownCurrentStage, setIsDownCurrentStage] = useState<boolean>(true);

  function onClearButtonSettings() {
    globalSettings.onNavigationBarDownButton('');
    globalSettings.onNavigationBarUpButton('');
  }
  function onSetDownButton(e: React.KeyboardEvent<HTMLButtonElement>) {
    if (e.shiftKey && e.key !== 'Shift') {
      globalSettings.onNavigationBarDownButton(e.key);
      setIsKeyDownFocused(false);
    }
  }
  function onSetUpButton(e: React.KeyboardEvent<HTMLButtonElement>) {
    if (
      e.shiftKey &&
      e.key !== 'Shift' &&
      e.key !== assignedButtons.downButtonKey
    ) {
      globalSettings.onNavigationBarUpButton(e.key);
      setIsKeyDownFocused(false);
    }
  }

  function onClickFocusBox() {
    setIsKeyDownFocused(true);

    if (isDownCurrentStage) {
      globalSettings.onNavigationBarDownButton('');
    } else {
      globalSettings.onNavigationBarUpButton('');
    }
  }

  useEffect(() => {
    let timeout: NodeJS.Timeout;
    if (assignedButtons.downButtonKey.length) {
      timeout = setTimeout(() => {
        setIsDownCurrentStage(false);
      }, 2000);
    }

    if (assignedButtons.upButtonKey.length) {
      timeout = setTimeout(() => {
        setIsKeySetting(false);
        setIsDownCurrentStage(true);
      }, 2000);
    }

    return () => {
      clearTimeout(timeout);
    };
  }, [assignedButtons.downButtonKey, assignedButtons.upButtonKey]);

  return (
    <MenuDropdownItem
      onYes={() => globalSettings.onIsNavigationBar(true)}
      onNo={() => globalSettings.onIsNavigationBar(false)}
      isYes={globalSettings.navigationBar.isOn}
      label="Navigation bar"
      onMore={() => null}
      isMore
      labelTooltip="Hold Shift and press S or W for default navigation"
    >
      {isKeySetting ? (
        <Flex justify={'center'} flexDir={'column'}>
          <Text textAlign={'center'} fontSize={'10px'}>
            Click on the box below then hold <Kbd>Shift</Kbd> and press a custom
            key for navigating {isDownCurrentStage ? <b>down</b> : <b>up</b>}.
          </Text>
          <Button
            onClick={onClickFocusBox}
            mx="auto"
            my="1"
            onKeyDown={isDownCurrentStage ? onSetDownButton : onSetUpButton}
            bg="gray.100"
            minW="30%"
            {...menuButtonStyle}
          >
            {isKeyDownFocused ? (
              <Text as={'i'} fontSize="10px">
                Press Button
              </Text>
            ) : (
              <Text fontSize="12px" fontWeight={'600'}>
                {isDownCurrentStage
                  ? assignedButtons.downButtonKey
                  : assignedButtons.upButtonKey}
              </Text>
            )}
          </Button>

          {(isDownCurrentStage && assignedButtons.downButtonKey) ||
          (!isDownCurrentStage && assignedButtons.upButtonKey) ? (
            <Text textAlign={'center'} fontStyle="italic" fontSize={'9px'}>
              wait 2 seconds to automatically go to the next step.
            </Text>
          ) : null}
        </Flex>
      ) : (
        <Flex align="center" justify={'space-evenly'}>
          {!assignedButtons.downButtonKey && !assignedButtons.upButtonKey ? (
            <Button
              onClick={() => setIsKeySetting(true)}
              fontSize={'10px'}
              {...menuButtonStyle}
            >
              set keys
            </Button>
          ) : (
            <>
              <Tooltip {...tooltipStyle} label="navigating down">
                <Kbd {...kbdStyle}>Shift + {assignedButtons.downButtonKey}</Kbd>
              </Tooltip>
              <Button
                onClick={onClearButtonSettings}
                fontSize={'10px'}
                {...menuButtonStyle}
              >
                clear keys
              </Button>
              <Tooltip {...tooltipStyle} label="navigating up">
                <Kbd {...kbdStyle}>Shift + {assignedButtons.upButtonKey}</Kbd>
              </Tooltip>
            </>
          )}
        </Flex>
      )}
    </MenuDropdownItem>
  );
}

export default NavigationBarFeature;
