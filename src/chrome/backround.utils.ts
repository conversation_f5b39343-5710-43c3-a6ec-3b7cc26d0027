import { ChromeMessage, ReactMessage, Sender } from '../types';

export const validateSender = (
  message: ChromeMessage,
  sender: chrome.runtime.MessageSender
) => {
  // console.log(message.message, 'BG MESSAGE');
  return (
    sender?.id === chrome?.runtime?.id && message.from === Sender.SummaryBadge
  );
};

export const validateReactSender = (
  message: ReactMessage,
  sender: chrome.runtime.MessageSender
) => {
  return sender?.id === chrome?.runtime?.id && message.from === Sender.React;
};
