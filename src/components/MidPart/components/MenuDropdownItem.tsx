import {
  But<PERSON>,
  Di<PERSON><PERSON>,
  Flex,
  Icon,
  SlideFade,
  Text,
  Tooltip,
} from "@chakra-ui/react";
import { ReactNode, useState } from "react";
import { BsGear, BsQuestionCircle } from "react-icons/bs";
import { menuButtonStyle, tooltipStyle } from "../../../styles/global.styles";
import ExclamationTooltip from "../../shared/components/ExclamationTooltip/ExclamationTooltip";

interface Props {
  label?: string;
  exclamationTooltip?: string;
  onYes?: () => void;
  onNo?: () => void;
  onMore?: () => void;
  isYes?: boolean;
  isMore?: boolean;
  isFeatureDisabled?: boolean;
  labelTooltip?: string;
  children?: ReactNode;
}

function MenuDropdownItem({
  label,
  isYes,
  onYes,
  onNo,
  isMore,
  isFeatureDisabled,
  labelTooltip,
  children,
  exclamationTooltip,
}: Props) {
  const [isMoreOpen, setIsMoreOpen] = useState(false);

  function onMore() {
    setIsMoreOpen((state) => !state);
  }

  return (
    <Flex
      rounded="md"
      transition="all 250ms"
      bg={isMoreOpen ? "rgb(255, 255, 255, 70%)" : "unset"}
      border="1px solid"
      borderColor={isMoreOpen ? "brand.purple" : "transparent"}
      pb="3px"
      flexDir="column"
      pt="3px"
      px="3px"
    >
      <Flex justify="space-between" align="center">
        <Flex pos="relative" align="center">
          <Text userSelect="none" fontSize={"xs"}>
            {label}:
          </Text>
          {labelTooltip ? (
            <Tooltip
              w="190px"
              bg="brand.paragraphColor"
              label={labelTooltip}
              {...tooltipStyle}
            >
              <Flex align="center" as="span" my="auto" h="18px">
                <Icon
                  ml="1"
                  cursor="pointer"
                  fontSize={"11px"}
                  as={BsQuestionCircle}
                />
              </Flex>
            </Tooltip>
          ) : null}

          {exclamationTooltip ? (
            <ExclamationTooltip label={exclamationTooltip} />
          ) : null}
        </Flex>
        <Flex align="center">
          {isMore && onMore && (
            <Tooltip
              bg="brand.paragraphColor"
              rounded="md"
              fontSize={"10px"}
              label="Click to expand additional settings"
              hasArrow
              arrowSize={6}
            >
              <Button
                isDisabled={isFeatureDisabled}
                pointerEvents={isFeatureDisabled ? "none" : "auto"}
                onClick={isFeatureDisabled ? undefined : onMore}
                bg={isMoreOpen ? "brand.purple" : "unset"}
                _hover={{ bg: "brand.purpleHover" }}
                {...menuButtonStyle}
              >
                <Icon as={BsGear} />
              </Button>
            </Tooltip>
          )}
          <Button
            isDisabled={isFeatureDisabled}
            pointerEvents={isFeatureDisabled ? "none" : "auto"}
            bg={!isYes ? "brand.noButtonColor" : "whiteAlpha.400"}
            color={isYes ? "brand.noButtonColor" : "white"}
            _focus={{
              bg: "brand.noButtonColor",
              color: "white",
            }}
            _selected={{
              bg: "brand.noButtonColor",
              color: "white",
            }}
            _hover={{
              bg: "brand.noButtonColorHover",
              color: "white",
            }}
            mx="1"
            display={onNo ? "flex" : "none"}
            onClick={isFeatureDisabled ? undefined : onNo}
            {...menuButtonStyle}
            fontWeight={600}

          >
            No
          </Button>
          <Button
            pointerEvents={isFeatureDisabled ? "none" : "auto"}
            isDisabled={isFeatureDisabled}
            display={onYes ? "flex" : "none"}
            bg={isYes ? "brand.yesButtonColor" : "whiteAlpha.400"}
            color={!isYes ? "brand.yesButtonColor" : "brand.paragraphColor"}
            _selected={{
              bg: "brand.yesButtonColor",
              color: "brand.paragraphColor",
            }}
            _focus={{
              bg: "brand.yesButtonColor",
              color: "brand.paragraphColor",
            }}
            _hover={{
              bg: "brand.yesButtonColorHover",
              color: "brand.paragraphColor",
            }}
            onClick={isFeatureDisabled ? undefined : onYes}
            {...menuButtonStyle}
            fontWeight={600}
          >
            Yes
          </Button>
        </Flex>
      </Flex>

      {/* MORE SETTINGS */}
      {isMoreOpen ? (
        <SlideFade offsetY="14" in={isMoreOpen}>
          <Flex pb="1" flexDir={"column"} roundedBottom={"md"} w="100%">
            <Divider
              mx="auto"
              bg="gray.900"
              opacity="0.15"
              rounded="xl"
              my="2"
              w="52%"
              h={"0.5px"}
            />
            {children}
          </Flex>
        </SlideFade>
      ) : null}
    </Flex>
  );
}

export default MenuDropdownItem;
