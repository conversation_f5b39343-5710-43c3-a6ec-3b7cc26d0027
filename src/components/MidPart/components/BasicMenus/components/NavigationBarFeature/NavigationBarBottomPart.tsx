import { Fade, Flex, Kbd, Text, Tooltip } from "@chakra-ui/react";
import { MouseEvent } from "react";
import useStateManager from "../../../../../../store/stateManager.hook";

import { kbdStyle, tooltipStyle } from "../../../../../../styles/global.styles";
import {
  hslFormatter,
  adaptiveFontColor,
} from "../../../../../shared/utils/color.utils";

function NavigationBarBottomPart() {
  const { globalSettings } = useStateManager();
  const assignedButtons = globalSettings.navigationBar.assignedButtons;

  function onCancelFocus(e: MouseEvent<HTMLDivElement>) {
    e.preventDefault();
    globalSettings.onNavigationBarWord(null);
  }

  if (!globalSettings.navigationBar.isOn) return null;
  return (
    <Fade in={globalSettings.navigationBar.isOn}>
      <Flex mb="2" justify={"space-evenly"} align="center">
        <>
          {assignedButtons.downButtonKey ? (
            <Tooltip {...tooltipStyle} label="navigating down">
              <Kbd {...kbdStyle}>Shift + {assignedButtons.downButtonKey}</Kbd>
            </Tooltip>
          ) : null}

          <Flex align="center" bg={"brand.purpleHover"} rounded="md">
            {globalSettings.navigationBar.focusedWord ? (
              <Tooltip
                {...tooltipStyle}
                label="Click to remove current focused query."
              >
                <Text
                  onClick={onCancelFocus}
                  color={adaptiveFontColor(
                    globalSettings.navigationBar?.focusedWord?.colors?.hsl
                  )}
                  rounded="md"
                  p="1"
                  bg={hslFormatter(
                    globalSettings.navigationBar?.focusedWord?.colors?.hsl
                  )}
                  fontSize={"10px"}
                  maxW="80px"
                  overflow={"scroll"}
                  maxH="30px"
                  cursor="pointer"
                  // overflow="hidden"
                >
                  {globalSettings.navigationBar.focusedWord.name}
                </Text>
              </Tooltip>
            ) : (
              <Tooltip {...tooltipStyle} label="Click on a query for focus">
                <Text
                  // ref={setNodeRef}
                  cursor="default"
                  fontStyle={"italic"}
                  px="1"
                  fontSize={"9px"}
                >
                  no focus
                </Text>
              </Tooltip>
            )}
          </Flex>
          {assignedButtons.upButtonKey ? (
            <Tooltip {...tooltipStyle} label="navigating up">
              <Kbd {...kbdStyle}>Shift + {assignedButtons.upButtonKey}</Kbd>
            </Tooltip>
          ) : null}
        </>
      </Flex>
    </Fade>
  );
}

export default NavigationBarBottomPart;
