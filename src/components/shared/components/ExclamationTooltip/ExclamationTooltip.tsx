import { Flex, Icon, Tooltip } from '@chakra-ui/react';
import { BsExclamationCircle } from 'react-icons/bs';
import { tooltipStyle } from '../../../../styles/global.styles';

interface Props {
  label?: string;
}

function ExclamationTooltip({
  label = 'The feature is still experimental.',
}: Props) {
  return (
    <Tooltip
      w="190px"
      bg="brand.paragraphColor"
      label={label}
      {...tooltipStyle}
    >
      <Flex align="center" as="span" my="auto" h="18px">
        <Icon
          color="red.500"
          ml="1"
          cursor="pointer"
          fontSize={'11px'}
          as={BsExclamationCircle}
        />
      </Flex>
    </Tooltip>
  );
}

export default ExclamationTooltip;
