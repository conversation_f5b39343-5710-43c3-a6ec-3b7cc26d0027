import {
  AuthCredential,
  User,
  browserLocalPersistence,
  createUserWithEmailAndPassword,
  deleteUser,
  onAuthStateChanged,
  reauthenticateWithCredential,
  sendEmailVerification,
  sendPasswordResetEmail,
  setPersistence,
  signInWithEmailAndPassword,
  signOut,
} from "firebase/auth";
import { deleteDoc, doc } from "firebase/firestore";
import { removeStorageDataLocal } from "../../../../chrome/chrome.utils";
import { auth, db } from "../../../../chrome/features/background.firebase";
import {
  UserAccount,
  UserSubscription,
} from "../../../../definitions/global.definition";
import { ToastTypesPopup } from "../../../shared/components/Toast/CustomToast.hook";
import { setAuthStateCache } from "./cachedAccount.utils";
import {
  getPaddleSubscription,
  refreshSubscriptionStorage,
} from "./paddle.utils";

// export function saveUserOnVendorLogin(userCredentials: UserCredential) {
//   const usersDoc = doc(db, 'users', userCredentials.user.uid);

//   setDoc(usersDoc, {
//     uid: userCredentials.user.uid,
//     email: userCredentials.user.email,
//     name: userCredentials.user.displayName,
//     provider: userCredentials.user.providerData[0].providerId,
//     photoUrl: userCredentials.user.photoURL,
//   });
// }

// export function singInWithProvider(isGoogle: boolean) {
//   const provider = isGoogle
//     ? new GoogleAuthProvider()
//     : new GithubAuthProvider();

//   signInWithPopup(auth, provider)
//     .then(async (userCredentials) => {
//       // Signed in
//       saveUserOnVendorLogin(userCredentials);
//       const user = userCredentials.user;
//       await setPersistence(auth, browserLocalPersistence);
//       console.log(user, 'userCredentials');
//       //   setAccount(userCredentials.user);
//     })
//     .catch((e: any) => {
//       console.log(e, 'singInWithProvider error');
//     });
// }

export function reAuthOnDelete(credential: AuthCredential) {
  if (!auth?.currentUser) return;
  reauthenticateWithCredential(auth?.currentUser, credential)
    .then(() => {
      // User re-authenticated.
    })
    .catch((error) => {
      // An error ocurred
      // ...
    });
}

async function deleteUserFirestore(
  customToast: (message: string, type: ToastTypesPopup) => void,
  id?: string
) {
  if (!id) return customToast("Please contact our support via mail.", "error");

  await deleteDoc(doc(db, "users", id));
}

export async function deleteAccount(
  setIsDeleteLoading: React.Dispatch<React.SetStateAction<boolean>>,
  setIsDeleteProps: React.Dispatch<React.SetStateAction<boolean>>,
  customToast: (message: string, type: ToastTypesPopup) => void
) {
  if (!auth?.currentUser) return;
  setIsDeleteLoading(true);

  try {
    await deleteUserFirestore(customToast, auth.currentUser?.uid);
  } catch (e: any) {
    // console.log(e, 'e');
    // customToast(e, 'error');
    customToast("An error has occured. Contact support.", "error");
    return;
  }

  deleteUser(auth?.currentUser)
    .then(async () => {
      customToast("Deletion successful!", "success");
      // User deleted.
    })
    .catch((e) => {
      setIsDeleteProps(true);
      customToast(trimError(e.message), "error");
      // An error ocurred
      // ...
    });
  setIsDeleteLoading(false);
}

export async function singInWithEmail(
  email: string,
  password: string,
  customToast: (message: string, type: ToastTypesPopup) => void,
  onSetIsAccountLoading: (isAccountLoading: boolean) => void,
  onSetAccount: (account: UserAccount) => void,
  onSetSubscription: (subscription: UserSubscription) => void,
  successCallback?: () => void
) {
  onSetIsAccountLoading(true);
  signInWithEmailAndPassword(auth, email, password)
    .then(async (userCredentials) => {
      // Signed in

      const user = userCredentials.user;
      console.log(user, "userCredentials");

      await setPersistence(auth, browserLocalPersistence);

      onSetAccount(user);

      const paddleResult = await getPaddleSubscription(user);

      onSetSubscription(paddleResult);
      setAuthStateCache(user, paddleResult);
      refreshSubscriptionStorage(paddleResult);

      customToast("Sign-in successful.", "success");
      onSetIsAccountLoading(false);
      if (successCallback) successCallback();
    })
    .catch((e) => {
      customToast(`Login failed! Try again.`, "error");
      onSetIsAccountLoading(false);
      // customToast(trimError(e.message), "error");
    });
}

export function onSignOut(
  customToast: (message: string, type: ToastTypesPopup) => void,
  onSetIsAccountLoading: (isAccountLoading: boolean) => void,
  onSetSubscription: (subscription: UserSubscription) => void,
  onSetAccount: (account: UserAccount) => void
) {
  onSetIsAccountLoading(true);
  signOut(auth)
    .then(() => {
      // Sign-out successful.

      removeStorageDataLocal("cachedAuth");
      onSetIsAccountLoading(false);
      onSetAccount(null);
      onSetSubscription(null);
      refreshSubscriptionStorage(null); // lokalisan uritse ki a jelenlegi subscriptiont logout eseten
      customToast("Sign-out successful.", "success");
    })
    .catch((e) => {
      // An error happened.
      customToast(trimError("Sign-out failed!"), "error");
      onSetIsAccountLoading(false);
    });
}

export function resetPassword(
  email: string,
  customToast: (message: string, type: ToastTypesPopup) => void
) {
  sendPasswordResetEmail(auth, email)
    .then(() => {
      customToast("Password reset email sent!", "success");
    })
    .catch((e) => {
      customToast(trimError("Password reset has failed."), "error");
    });
}

export async function sendVerificationAgain(
  customToast: (message: string, type: ToastTypesPopup) => void
) {
  if (!auth.currentUser) return;
  try {
    await sendEmailVerification(auth.currentUser);

    customToast("Check your inbox/spam!", "success");
  } catch (e: any) {
    customToast("Verification has failed.", "error");
    // console.log(e, "verification error");
  }
}

export function signUpWithEmail(
  email: string,
  password: string,
  customToast: (message: string, type: ToastTypesPopup) => void,
  onSetIsAccountLoading: (isAccountLoading: boolean) => void
) {
  createUserWithEmailAndPassword(auth, email, password)
    .then(async (userCredentials) => {
      // Signed in

      const user = userCredentials.user;

      await sendEmailVerification(user);

      await setPersistence(auth, browserLocalPersistence);

      customToast("Successful registration!", "success");
    })
    .catch((e) => {
      customToast(trimError("Registration has failed!"), "error");
    });
}

export function authStateChangedObserver(
  onSetAccount: (account: UserAccount) => void,
  onSetIsAccountLoading: (isAccountLoading: boolean) => void,
  onSetSubscription: (subscription: UserSubscription) => void,
  setAuthStateCache: (
    user: User | null,
    paddle: UserSubscription | null
  ) => void
) {
  onAuthStateChanged(auth, async (user) => {
    if (user) {
      onSetAccount(user);

      const paddleResult = await getPaddleSubscription(user);

      onSetSubscription(paddleResult);
      setAuthStateCache(user, paddleResult);
      refreshSubscriptionStorage(paddleResult);
    } else {
      onSetAccount(null);
      onSetSubscription(null);
      refreshSubscriptionStorage(null); // lokalisan uritse ki a jelenlegi subscriptiont logout eseten
      setAuthStateCache(null, null);
    }

    onSetIsAccountLoading(false);
  });
}

export function trimError(text: string) {
  if (!text) return "";

  return text.substring(text.indexOf("(") + 1, text.lastIndexOf(")"));
}
