import useStateManager from "../../../../../store/stateManager.hook";
import MenuDropdownItem from "../../MenuDropdownItem";

function CompleteSearchFeature() {
  const { globalSettings } = useStateManager();

  return (
    <MenuDropdownItem
      onYes={() => globalSettings.onIsCompleteWordSearch(true)}
      onNo={() => globalSettings.onIsCompleteWordSearch(false)}
      isYes={globalSettings.completeWordSearch.isOn}
      label="Complete phrase search"
      labelTooltip="Enable this to search for complete phrases. This way you can search for 'hello world' and it will only return results that contain 'hello world' and not 'hello world123' or 'hello world!'."
    />
  );
}

export default CompleteSearchFeature;
