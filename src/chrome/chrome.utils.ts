import { ChromeMessage, ReactMessage, Sender } from "../types";

// export const getStorageData = <TResponse>(key: string) =>
//   chrome.storage?.sync
//     ? new Promise((resolve, reject) =>
//         chrome.storage.sync.get(key, (result) =>
//           chrome.runtime.lastError
//             ? reject(Error(chrome.runtime.lastError.message))
//             : (resolve(result) as TResponse)
//         )
//       )
//     : undefined;

// export const setStorageData = <TData>(data: Record<string, TData>) =>
//   chrome.storage?.sync
//     ? new Promise((resolve, reject) =>
//         chrome.storage.sync.set(data, () =>
//           chrome.runtime.lastError
//             ? reject(Error(chrome.runtime.lastError.message))
//             : resolve((data: TData) => {
//                 return data;
//               })
//         )
//       )
//     : undefined;

export const getStorageDataLocal = <TResponse>(
  key: string
): Promise<TResponse> | undefined => {
  if (typeof browser !== "undefined" && browser.storage.local) {
    return new Promise((resolve, reject) => {
      browser.storage.local.get(key).then(
        (result: any) => {
          resolve(result as TResponse);
        },
        (error: any) => reject(Error(error.message))
      );
    });
  } else if (typeof chrome !== "undefined" && chrome.storage?.local) {
    return new Promise((resolve, reject) => {
      chrome.storage.local.get(key, (result: any) =>
        chrome.runtime.lastError
          ? reject(Error(chrome.runtime.lastError.message))
          : resolve(result as TResponse)
      );
    });
  } else {
    return undefined;
  }
};

export const setStorageDataLocal = <TData>(data: Record<string, TData>) => {
  if (typeof browser !== "undefined" && browser.storage.local) {
    return new Promise<void>((resolve, reject) => {
      browser.storage.local.set(data).then(
        () => resolve(),
        (error: any) => reject(Error(error.message))
      );
    });
  } else if (typeof chrome !== "undefined" && chrome.storage?.local) {
    return new Promise<void>((resolve, reject) => {
      chrome.storage.local.set(data, () =>
        chrome.runtime.lastError
          ? reject(Error(chrome.runtime.lastError.message))
          : resolve()
      );
    });
  } else {
    return undefined;
  }
};

export const removeStorageDataLocal = (
  key: string | string[]
): Promise<void> => {
  if (typeof browser !== "undefined" && browser.storage?.local) {
    // For Firefox or browsers using the `browser` API
    return browser.storage.local.remove(key);
  } else if (typeof chrome !== "undefined" && chrome.storage?.local) {
    // For Chrome or browsers using the `chrome` API
    return new Promise((resolve, reject) => {
      chrome.storage.local.remove(key, () => {
        if (chrome.runtime.lastError) {
          reject(Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  } else {
    // If neither `browser` nor `chrome` storage is available
    return Promise.reject(new Error("Storage API not available"));
  }
};

export const removeStorageData = (key: string) => {
  if (typeof chrome !== "undefined" && chrome.storage?.sync) {
    return new Promise<void>((resolve, reject) => {
      chrome.storage.sync.remove(key, () => {
        if (chrome.runtime.lastError) {
          reject(Error(chrome.runtime.lastError.message));
        } else {
          console.log("removed everything");
          resolve();
        }
      });
    });
  } else if (typeof browser !== "undefined" && browser.storage?.sync) {
    return new Promise<void>((resolve, reject) => {
      browser.storage.sync.remove(key).then(
        () => {
          console.log("removed everything");
          resolve();
        },
        (error: any) => reject(Error(error.message))
      );
    });
  } else {
    return undefined;
  }
};

export const clearStorageData = () => {
  if (typeof chrome !== "undefined" && chrome.storage?.sync) {
    return new Promise<void>((resolve, reject) => {
      chrome.storage.sync.clear(() => {
        if (chrome.runtime.lastError) {
          reject(Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  } else if (typeof browser !== "undefined" && browser.storage?.sync) {
    return new Promise<void>((resolve, reject) => {
      browser.storage.sync.clear().then(
        () => resolve(),
        (error: any) => reject(Error(error.message))
      );
    });
  } else {
    return undefined;
  }
};

export const clearStorageDataLocal = () => {
  if (typeof chrome !== "undefined" && chrome.storage?.local) {
    return new Promise<void>((resolve, reject) => {
      chrome.storage.local.clear(() => {
        if (chrome.runtime.lastError) {
          reject(Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  } else if (typeof browser !== "undefined" && browser.storage?.local) {
    return new Promise<void>((resolve, reject) => {
      browser.storage.local.clear().then(
        () => resolve(),
        (error: any) => reject(Error(error.message))
      );
    });
  } else {
    return undefined;
  }
};

export const sendToTab = <TMessage>(messageValue: TMessage) => {
  if (
    (typeof chrome !== "undefined" && chrome.tabs) ||
    (typeof browser !== "undefined" && browser.tabs)
  ) {
    const tabsAPI = typeof chrome !== "undefined" ? chrome.tabs : browser.tabs;
    tabsAPI.query(
      {
        active: true,
        currentWindow: true,
      },
      function (tabs) {
        const message: ChromeMessage = {
          from: Sender.React,
          message: messageValue,
        };

        if (tabs[0]?.id) tabsAPI.sendMessage(tabs[0].id as number, message);
      }
    );
  }
};
export const sendToAllTabs = <TMessage>(messageValue: TMessage) => {
  if (
    (typeof chrome !== "undefined" && chrome.tabs) ||
    (typeof browser !== "undefined" && browser.tabs)
  ) {
    const tabsAPI = typeof chrome !== "undefined" ? chrome.tabs : browser.tabs;
    tabsAPI.query({}, function (tabs) {
      const message = {
        from: Sender.React,
        message: messageValue,
      };

      for (let i = 0; i < tabs.length; ++i) {
        if (tabs[i]?.id) {
          tabsAPI.sendMessage(tabs[i]?.id as number, message, () => {});
        }
      }
    });
  }
};

export const sendMessageTo = <TResponse>(messageValue: ReactMessage) => {
  if (
    (typeof chrome !== "undefined" && chrome.runtime.sendMessage) ||
    (typeof browser !== "undefined" && browser.runtime.sendMessage)
  ) {
    const runtimeAPI =
      typeof chrome !== "undefined" ? chrome.runtime : browser.runtime;

    if (typeof chrome !== "undefined") {
      return new Promise<TResponse>((resolve, reject) => {
        chrome.runtime.sendMessage(messageValue, (response: TResponse) => {
          if (runtimeAPI.lastError) {
            reject(Error(runtimeAPI.lastError.message));
          } else {
            resolve(response);
          }
        });
      });
    } else {
      return new Promise<TResponse>((resolve, reject) => {
        browser.runtime.sendMessage("", messageValue);
      });
    }
  } else {
    return undefined;
  }
};
