import DOMIterator from "./domiterator";

/**
 * Marks search terms in DOM elements
 * @example
 * new Mark(document.querySelector(".context")).mark("lorem ipsum");
 * @example
 * new Mark(document.querySelector(".context")).markRegExp(/lorem/gmi);
 */
export default class Mark {
  /**
   * Cache for diacritics RegExp creation to improve performance
   * @type {Map}
   * @static
   */
  static diacriticsCache = new Map();

  /**
   * @param {HTMLElement|HTMLElement[]|NodeList|string} ctx - The context DOM
   * element, an array of DOM elements, a NodeList or a selector
   */
  constructor(ctx) {
    this.ctx = ctx;
    this.ie = /MSIE|Trident/.test(window.navigator.userAgent);
  }

  /**
   * Options defined by the user.
   * @type {object}
   * @param {object} [val] - An object that will be merged with defaults
   */
  set opt(val) {
    this._opt = {
      element: "",
      className: "",
      exclude: [],
      iframes: false,
      iframesTimeout: 5000,
      separateWordSearch: true,
      diacritics: true,
      synonyms: {},
      accuracy: "partially",
      acrossElements: false,
      caseSensitive: false,
      ignoreJoiners: false,
      ignoreGroups: 0,
      ignorePunctuation: [],
      wildcards: "disabled",
      each: () => {},
      noMatch: () => {},
      filter: () => true,
      done: () => {},
      debug: false,
      log: window.console,
      ...val,
    };
  }

  get opt() {
    return this._opt;
  }

  /**
   * An instance of DOMIterator
   * @type {DOMIterator}
   */
  get iterator() {
    return new DOMIterator(
      this.ctx,
      this.opt.iframes,
      this.opt.exclude,
      this.opt.iframesTimeout
    );
  }

  /**
   * Logs a message if log is enabled
   * @param {string} msg - The message to log
   * @param {string} [level="debug"] - The log level
   */
  log(msg, level = "debug") {
    if (this.opt.debug && this.opt.log[level]) {
      this.opt.log[level](`mark.js: ${msg}`);
    }
  }

  /**
   * Escapes a string for usage within a regular expression
   * @param {string} str - The string to escape
   * @return {string}
   */
  escapeStr(str) {
    return str.replace(/[-\/\\^$*+?.()|[\]{}]/g, "\\$&");
  }

  /**
   * Creates a regular expression string to match the specified search term
   * @param {string} str - The search term to be used
   * @return {string}
   */
  createRegExp(str) {
    // Store the original string before any processing
    const originalStr = str;

    // Apply wildcards first if enabled
    if (this.opt.wildcards !== "disabled") {
      str = this.setupWildcardsRegExp(str);
    }

    // Always escape the string
    str = this.escapeStr(str);

    // Apply synonyms if any exist
    if (Object.keys(this.opt.synonyms).length) {
      str = this.createSynonymsRegExp(str);
    }

    // Handle joiners and punctuation in one pass if possible
    const needsJoinersHandling = this.opt.ignoreJoiners || this.opt.ignorePunctuation.length;
    if (needsJoinersHandling) {
      str = this.setupIgnoreJoinersRegExp(str);
    }

    // Apply diacritics handling
    if (this.opt.diacritics) {
      str = this.createDiacriticsRegExp(str);
    }

    // Handle merged blanks
    str = this.createMergedBlanksRegExp(str);

    // Apply joiners RegExp if needed
    if (needsJoinersHandling) {
      str = this.createJoinersRegExp(str);
    }

    // Apply wildcards RegExp if enabled
    if (this.opt.wildcards !== "disabled") {
      str = this.createWildcardsRegExp(str);
    }

    // Check if we need to handle this differently for separateWordSearch
    if (!this.opt.separateWordSearch) {
      // When separateWordSearch is false, we need to check if this is a partial match
      // of a multi-word search term

      // Get the original search terms
      const originalTerms = this.opt._originalSearchTerms || [];

      // Check if this is one of the original search terms
      const isOriginalTerm = originalTerms.includes(originalStr);

      // If this is not an original search term and we have original terms,
      // check if it's a partial match of a multi-word term
      if (!isOriginalTerm && originalTerms.length > 0) {
        // Check if this term is a part of any multi-word original term
        const isPartOfMultiWord = originalTerms.some(term => {
          // Skip if this is the term itself
          if (term === originalStr) {
            return false;
          }

          // Check if the term contains spaces (is multi-word)
          // and if it contains this search term
          return term.includes(' ') && term.includes(originalStr);
        });

        // If this is a part of a multi-word term and separateWordSearch is false,
        // we should not match it
        if (isPartOfMultiWord) {
          return "(?!)"; // This is a regex that never matches anything
        }
      }
    }

    // Finally, create the accuracy RegExp
    return this.createAccuracyRegExp(str);
  }

  /**
   * Creates a regular expression string to match the defined synonyms
   * @param {string} str - The search term to be used
   * @return {string}
   */
  createSynonymsRegExp(str) {
    const { synonyms, caseSensitive } = this.opt;
    const sens = caseSensitive ? "" : "i";
    const joinerPlaceholder =
      this.opt.ignoreJoiners || this.opt.ignorePunctuation.length
        ? "\u0000"
        : "";

    Object.entries(synonyms).forEach(([key, value]) => {
      const k1 =
        this.opt.wildcards !== "disabled"
          ? this.setupWildcardsRegExp(key)
          : this.escapeStr(key);
      const k2 =
        this.opt.wildcards !== "disabled"
          ? this.setupWildcardsRegExp(value)
          : this.escapeStr(value);
      if (k1 && k2) {
        str = str.replace(
          new RegExp(
            `(${this.escapeStr(k1)}|${this.escapeStr(k2)})`,
            `gm${sens}`
          ),
          `${joinerPlaceholder}(${this.processSynonyms(
            k1
          )}|${this.processSynonyms(k2)})${joinerPlaceholder}`
        );
      }
    });
    return str;
  }

  /**
   * Processes synonyms to work with ignoreJoiners and/or ignorePunctuation
   * @param {string} str - synonym key or value to process
   * @return {string} - processed synonym string
   */
  processSynonyms(str) {
    if (this.opt.ignoreJoiners || this.opt.ignorePunctuation.length) {
      str = this.setupIgnoreJoinersRegExp(str);
    }
    return str;
  }

  /**
   * Sets up the regular expression string to allow later insertion of wildcard matches
   * @param {string} str - The search term to be used
   * @return {string}
   */
  setupWildcardsRegExp(str) {
    return str
      .replace(/(?:\\)*\?/g, (val) => (val.charAt(0) === "\\" ? "?" : "\u0001"))
      .replace(/(?:\\)*\*/g, (val) =>
        val.charAt(0) === "\\" ? "*" : "\u0002"
      );
  }

  /**
   * Creates a regular expression string to allow ignoring of designated characters
   * @param {string} str - The search term to be used
   * @return {string}
   */
  createWildcardsRegExp(str) {
    const spaces = this.opt.wildcards === "withSpaces";
    return str
      .replace(/\u0001/g, spaces ? "[\\S\\s]?" : "\\S?")
      .replace(/\u0002/g, spaces ? "[\\S\\s]*?" : "\\S*");
  }

  /**
   * Sets up the regular expression string to allow later insertion of designated characters
   * @param {string} str - The search term to be used
   * @return {string}
   */
  setupIgnoreJoinersRegExp(str) {
    return str.replace(/[^(|)\\]/g, (val, indx, original) => {
      const nextChar = original.charAt(indx + 1);
      return /[(|)\\]/.test(nextChar) || nextChar === "" ? val : val + "\u0000";
    });
  }

  /**
   * Creates a regular expression string to allow ignoring of designated characters
   * @param {string} str - The search term to be used
   * @return {string}
   */
  createJoinersRegExp(str) {
    const joiner = [];
    if (
      Array.isArray(this.opt.ignorePunctuation) &&
      this.opt.ignorePunctuation.length
    ) {
      joiner.push(this.escapeStr(this.opt.ignorePunctuation.join("")));
    }
    if (this.opt.ignoreJoiners) {
      joiner.push("\\u00ad\\u200b\\u200c\\u200d");
    }
    return joiner.length
      ? str.split(/\u0000+/).join(`[${joiner.join("")}]*`)
      : str;
  }

  /**
   * Creates a regular expression string to match diacritics
   * @param {string} str - The search term to be used
   * @return {string}
   */
  createDiacriticsRegExp(str) {
    const { caseSensitive } = this.opt;
    const cacheKey = `${str}_${caseSensitive}_${this.opt.diacritics}`;

    // Check if we have a cached result
    if (Mark.diacriticsCache.has(cacheKey)) {
      return Mark.diacriticsCache.get(cacheKey);
    }

    // In mark.js, diacritics: true means "ignore diacritics" (diacritic insensitive)
    // and diacritics: false means "consider diacritics" (diacritic sensitive)

    // If diacritics is false, we should return the string as is (diacritic sensitive)
    if (this.opt.diacritics === false) {
      // Cache the result
      Mark.diacriticsCache.set(cacheKey, str);
      return str;
    }

    // Define character mappings for diacritic insensitivity
    // Each base character maps to a character class of all its diacritic variants
    const diacriticMap = {
      'a': '[aáàâäãåāăąǎǟǡǻȁȃȧḁạảấầẩẫậắằẳẵặ]',
      'A': '[AÁÀÂÄÃÅĀĂĄǍǞǠǺȀȂȦḀẠẢẤẦẨẪẬẮẰẲẴẶ]',
      'b': '[bḃḅḇƀƃɓ]',
      'B': '[BḂḄḆɃƂƁ]',
      'c': '[cçćĉċčƈȼḉ]',
      'C': '[CÇĆĈĊČƇȻḈ]',
      'd': '[dďḋḍḏḑḓđƌɖɗ]',
      'D': '[DĎḊḌḎḐḒĐƋƉƊ]',
      'e': '[eéèêëēĕėęěȅȇȩḕḗḙḛḝẹẻẽếềểễệ]',
      'E': '[EÉÈÊËĒĔĖĘĚȄȆȨḔḖḘḚḜẸẺẼẾỀỂỄỆ]',
      'f': '[fḟƒ]',
      'F': '[FḞƑ]',
      'g': '[gĝğġģǥǧǵḡƣ]',
      'G': '[GĜĞĠĢǤǦǴḠƢ]',
      'h': '[hĥħḣḥḧḩḫẖ]',
      'H': '[HĤĦḢḤḦḨḪ]',
      'i': '[iíìîïĩīĭįǐȉȋḭḯỉịĳ]',
      'I': '[IÍÌÎÏĨĪĬĮǏȈȊḬḮỈỊĲ]',
      'j': '[jĵǰ]',
      'J': '[JĴ]',
      'k': '[kķǩḱḳḵƙ]',
      'K': '[KĶǨḰḲḴƘ]',
      'l': '[lĺļľḷḹḻḽƚł]',
      'L': '[LĹĻĽḶḸḺḼȽŁ]',
      'm': '[mḿṁṃ]',
      'M': '[MḾṀṂ]',
      'n': '[nñńņňǹṅṇṉṋ]',
      'N': '[NÑŃŅŇǸṄṆṈṊ]',
      'o': '[oóòôöõøōŏőơǒǫǭȍȏȫȭȯȱṍṏṑṓọỏốồổỗộớờởỡợ]',
      'O': '[OÓÒÔÖÕØŌŎŐƠǑǪǬȌȎȪȬȮȰṌṎṐṒỌỎỐỒỔỖỘỚỜỞỠỢ]',
      'p': '[pṕṗƥ]',
      'P': '[PṔṖƤ]',
      'q': '[q]',
      'Q': '[Q]',
      'r': '[rŕŗřȑȓṙṛṝṟ]',
      'R': '[RŔŖŘȐȒṘṚṜṞ]',
      's': '[sśŝşšșṡṣṥṧṩ]',
      'S': '[SŚŜŞŠȘṠṢṤṦṨ]',
      't': '[tţťțṫṭṯṱƭʈ]',
      'T': '[TŢŤȚṪṬṮṰƬ]',
      'u': '[uúùûüũūŭůűųưǔǖǘǚǜȕȗṳṵṷṹṻụủứừửữự]',
      'U': '[UÚÙÛÜŨŪŬŮŰŲƯǓǕǗǙǛȔȖṲṴṶṸṺỤỦỨỪỬỮỰ]',
      'v': '[vṽṿ]',
      'V': '[VṼṾ]',
      'w': '[wŵẁẃẅẇẉ]',
      'W': '[WŴẀẂẄẆẈ]',
      'x': '[xẋẍ]',
      'X': '[XẊẌ]',
      'y': '[yýỳŷÿỹȳẏỵỷỹ]',
      'Y': '[YÝỲŶŸỸȲẎỴỶỸ]',
      'z': '[zźżžẑẓẕ]',
      'Z': '[ZŹŻŽẐẒẔ]'
    };

    // Special handling for Hungarian characters
    const hungarianMap = {
      'a': 'aá',
      'e': 'eé',
      'i': 'ií',
      'o': 'oóöő',
      'u': 'uúüű',
      'A': 'AÁ',
      'E': 'EÉ',
      'I': 'IÍ',
      'O': 'OÓÖŐ',
      'U': 'UÚÜŰ'
    };

    // Replace each character with its diacritic-insensitive pattern
    let result = '';
    for (let i = 0; i < str.length; i++) {
      const ch = str[i];

      // Check if we have a mapping for this character
      if (diacriticMap[ch]) {
        result += diacriticMap[ch];
      } else if (hungarianMap[ch]) {
        // Special handling for Hungarian characters
        result += `[${hungarianMap[ch]}]`;
      } else {
        // No mapping, use the character as is
        result += ch;
      }
    }

    // Cache the result
    Mark.diacriticsCache.set(cacheKey, result);
    return result;
  }

  /**
   * Creates a regular expression string that merges whitespace characters
   * @param {string} str - The search term to be used
   * @return {string}
   */
  createMergedBlanksRegExp(str) {
    return str.replace(/\s+/g, "[\\s]+");
  }

  /**
   * Creates a regular expression string to match the specified string with the defined accuracy
   * @param {string} str - The search term to be used
   * @return {string}
   */

  createAccuracyRegExp(str) {
    const { accuracy, completeWordSearch } = this.opt;
    const val = typeof accuracy === "string" ? accuracy : accuracy.value;
    const ls = typeof accuracy === "string" ? [] : accuracy.limiters;
    const lsJoin = ls.map((limiter) => this.escapeStr(limiter)).join("|");

    // If completeWordSearch is true, we need to use strict word boundaries
    if (completeWordSearch) {
      // Use word boundaries to ensure we're only matching complete words
      // This ensures that "Anya," will not match when searching for "Anya"
      return `(^|\\s)(${str})(?=$|\\s)`;
    }

    // Standard behavior for all other cases
    switch (val) {
      case "complementary":
        return `()([^\\s${lsJoin}]*${str}[^\\s${lsJoin}]*)`;
      case "exactly":
        return `(^|[\\s\\/\\-.,:;!?()"'>]+|>)(${str})(?=$|[\\s\\/\\-.,:;!?()"'<]+|<|\\s)`;
      case "partially":
      default:
        return `()(${str})`;
    }
  }

  /**
   * Returns a list of keywords dependent on whether separate word search was defined
   * @param {array} sv - The array of keywords
   * @return {object}
   */
  getSeparatedKeywords(sv) {
    // Use a Set for faster lookups and automatic deduplication
    const keywordSet = new Set();

    sv.forEach((kw) => {
      if (!this.opt.separateWordSearch) {
        // When separateWordSearch is false, we only want to match the complete term
        if (kw.trim()) {
          keywordSet.add(kw);
        }
      } else {
        kw.split(" ").forEach((kwSplitted) => {
          if (kwSplitted.trim()) {
            keywordSet.add(kwSplitted);
          }
        });
      }
    });

    // Convert to array and sort
    const stack = Array.from(keywordSet).sort((a, b) => b.length - a.length);

    return {
      keywords: stack,
      length: stack.length,
    };
  }

  /**
   * Check if a value is a number
   * @param {number|string} value - the value to check
   * @return {boolean}
   */
  isNumeric(value) {
    return Number(parseFloat(value)) == value; // eslint-disable-line eqeqeq
  }

  /**
   * Returns a processed list of integer offset indexes that do not overlap each other
   * @param {array} array - unprocessed raw array
   * @return {array} - processed array with any invalid entries removed
   */
  checkRanges(array) {
    if (
      !Array.isArray(array) ||
      Object.prototype.toString.call(array[0]) !== "[object Object]"
    ) {
      this.log("markRanges() will only accept an array of objects");
      this.opt.noMatch(array);
      return [];
    }

    const stack = [];
    let last = 0;
    array
      .sort((a, b) => a.start - b.start)
      .forEach((item) => {
        const { start, end, valid } = this.callNoMatchOnInvalidRanges(
          item,
          last
        );
        if (valid) {
          item.start = start;
          item.length = end - start;
          stack.push(item);
          last = end;
        }
      });
    return stack;
  }

  /**
   * Initial validation of ranges for markRanges
   * @param {object} range - the current range object
   * @param {number} last - last index of range
   * @return {object}
   */
  callNoMatchOnInvalidRanges(range, last) {
    let start,
      end,
      valid = false;
    if (range && typeof range.start !== "undefined") {
      start = parseInt(range.start, 10);
      end = start + parseInt(range.length, 10);
      if (
        this.isNumeric(range.start) &&
        this.isNumeric(range.length) &&
        end - last > 0 &&
        end - start > 0
      ) {
        valid = true;
      } else {
        this.log(
          `Ignoring invalid or overlapping range: ${JSON.stringify(range)}`
        );
        this.opt.noMatch(range);
      }
    } else {
      this.log(`Ignoring invalid range: ${JSON.stringify(range)}`);
      this.opt.noMatch(range);
    }
    return { start, end, valid };
  }

  /**
   * Check valid range for markRanges
   * @param {object} range - the current range object
   * @param {number} originalLength - original length of the context string
   * @param {string} string - current content string
   * @return {object}
   */
  checkWhitespaceRanges(range, originalLength, string) {
    const max = string.length;
    const offset = originalLength - max;
    let start = parseInt(range.start, 10) - offset;
    start = start > max ? max : start;
    let end = start + parseInt(range.length, 10);
    if (end > max) {
      end = max;
      this.log(`End range automatically set to the max value of ${max}`);
    }
    const valid = !(
      start < 0 ||
      end - start < 0 ||
      start > max ||
      end > max ||
      string.substring(start, end).replace(/\s+/g, "") === ""
    );
    if (!valid) {
      this.log(
        `Skipping invalid or whitespace only range: ${JSON.stringify(range)}`
      );
      this.opt.noMatch(range);
    }
    return { start, end, valid };
  }

  /**
   * Calls the callback with an object containing all text nodes
   * @param {function} cb - Callback
   */
  getTextNodes(cb) {
    let val = "";
    const nodes = [];
    this.iterator.forEachNode(
      NodeFilter.SHOW_TEXT,
      (node) => {
        nodes.push({
          start: val.length,
          end: (val += node.textContent).length,
          node,
        });
      },
      (node) =>
        this.matchesExclude(node.parentNode)
          ? NodeFilter.FILTER_REJECT
          : NodeFilter.FILTER_ACCEPT,
      () => cb({ value: val, nodes })
    );
  }

  /**
   * Checks if an element matches any of the specified exclude selectors
   * @param {HTMLElement} el - The element to check
   * @return {boolean}
   */
  matchesExclude(el) {
    return DOMIterator.matches(
      el,
      this.opt.exclude.concat(["script", "style", "title", "head", "html"])
    );
  }

  /**
   * Wraps the instance element and class around matches
   * @param {HTMLElement} node - The DOM text node
   * @param {number} start - The position where to start wrapping
   * @param {number} end - The position where to end wrapping
   * @return {HTMLElement}
   */
  wrapRangeInTextNode(node, start, end) {
    const hEl = this.opt.element || "mark";
    const startNode = node.splitText(start);
    const ret = startNode.splitText(end - start);
    const repl = document.createElement(hEl);
    repl.setAttribute("is-highlighty", "true");
    if (this.opt.className) {
      repl.setAttribute("class", this.opt.className);
    }
    repl.textContent = startNode.textContent;
    startNode.parentNode.replaceChild(repl, startNode);
    return ret;
  }

  /**
   * Determines matches by start and end positions using the text node dictionary
   * @param {object} dict - The dictionary
   * @param {number} start - The start position of the match
   * @param {number} end - The end position of the match
   * @param {function} filterCb - Filter callback
   * @param {function} eachCb - Each callback
   */
  wrapRangeInMappedTextNode(dict, start, end, filterCb, eachCb) {
    dict.nodes.every((n, i) => {
      const sibl = dict.nodes[i + 1];
      if (!sibl || sibl.start > start) {
        if (!filterCb(n.node)) {
          return false;
        }
        const s = start - n.start;
        const e = (end > n.end ? n.end : end) - n.start;
        const startStr = dict.value.substr(0, n.start);
        const endStr = dict.value.substr(e + n.start);
        n.node = this.wrapRangeInTextNode(n.node, s, e);
        dict.value = startStr + endStr;
        dict.nodes.forEach((_, j) => {
          if (j >= i) {
            if (dict.nodes[j].start > 0 && j !== i) {
              dict.nodes[j].start -= e;
            }
            dict.nodes[j].end -= e;
          }
        });
        end -= e;
        eachCb(n.node.previousSibling, n.start);
        if (end > n.end) {
          start = n.end;
        } else {
          return false;
        }
      }
      return true;
    });
  }

  /**
   * Wraps the instance element and class around matches within single HTML elements
   * @param {RegExp} regex - The regular expression to be searched for
   * @param {number} ignoreGroups - A number indicating the amount of RegExp matching groups to ignore
   * @param {function} filterCb - Filter callback
   * @param {function} eachCb - Each callback
   * @param {function} endCb - End callback
   */
  wrapMatches(regex, ignoreGroups, filterCb, eachCb, endCb) {
    const matchIdx = ignoreGroups === 0 ? 0 : ignoreGroups + 1;
    this.getTextNodes((dict) => {
      dict.nodes.forEach(({ node }) => {
        let match;
        while (
          (match = regex.exec(node.textContent)) !== null &&
          match[matchIdx] !== ""
        ) {
          if (!filterCb(match[matchIdx], node)) {
            continue;
          }
          let pos = match.index;
          if (matchIdx !== 0) {
            for (let i = 1; i < matchIdx; i++) {
              pos += match[i].length;
            }
          }
          node = this.wrapRangeInTextNode(
            node,
            pos,
            pos + match[matchIdx].length
          );
          eachCb(node.previousSibling);
          regex.lastIndex = 0;
        }
      });
      endCb();
    });
  }

  /**
   * Wraps the instance element and class around matches across all HTML elements
   * @param {RegExp} regex - The regular expression to be searched for
   * @param {number} ignoreGroups - A number indicating the amount of RegExp matching groups to ignore
   * @param {function} filterCb - Filter callback
   * @param {function} eachCb - Each callback
   * @param {function} endCb - End callback
   */
  wrapMatchesAcrossElements(regex, ignoreGroups, filterCb, eachCb, endCb) {
    const matchIdx = ignoreGroups === 0 ? 0 : ignoreGroups + 1;
    this.getTextNodes((dict) => {
      let match;
      while (
        (match = regex.exec(dict.value)) !== null &&
        match[matchIdx] !== ""
      ) {
        let start = match.index;
        if (matchIdx !== 0) {
          for (let i = 1; i < matchIdx; i++) {
            start += match[i].length;
          }
        }
        const end = start + match[matchIdx].length;
        // Store the match value to avoid the unsafe reference in the callback
        const matchVal = match[matchIdx];

        this.wrapRangeInMappedTextNode(
          dict,
          start,
          end,
          (node) => filterCb(matchVal, node),
          (node, lastIndex) => {
            regex.lastIndex = lastIndex;
            eachCb(node);
          }
        );
      }
      endCb();
    });
  }

  /**
   * Wraps the indicated ranges across all HTML elements
   * @param {array} ranges - The array of objects
   * @param {function} filterCb - Filter callback
   * @param {function} eachCb - Each callback
   * @param {function} endCb - End callback
   */
  wrapRangeFromIndex(ranges, filterCb, eachCb, endCb) {
    this.getTextNodes((dict) => {
      const originalLength = dict.value.length;
      ranges.forEach((range, counter) => {
        const { start, end, valid } = this.checkWhitespaceRanges(
          range,
          originalLength,
          dict.value
        );
        if (valid) {
          this.wrapRangeInMappedTextNode(
            dict,
            start,
            end,
            (node) =>
              filterCb(node, range, dict.value.substring(start, end), counter),
            (node) => eachCb(node, range)
          );
        }
      });
      endCb();
    });
  }

  /**
   * Unwraps the specified DOM node with its content
   * @param {HTMLElement} node - The DOM node to unwrap
   */
  unwrapMatches(node) {
    const parent = node.parentNode;
    const docFrag = document.createDocumentFragment();
    while (node.firstChild) {
      docFrag.appendChild(node.removeChild(node.firstChild));
    }
    parent.replaceChild(docFrag, node);
    if (!this.ie) {
      parent.normalize();
    } else {
      this.normalizeTextNode(parent);
    }
  }

  /**
   * Normalizes text nodes
   * @param {HTMLElement} node - The DOM node to normalize
   */
  normalizeTextNode(node) {
    if (!node) return;
    if (node.nodeType === 3) {
      while (node.nextSibling && node.nextSibling.nodeType === 3) {
        node.nodeValue += node.nextSibling.nodeValue;
        node.parentNode.removeChild(node.nextSibling);
      }
    } else {
      this.normalizeTextNode(node.firstChild);
    }
    this.normalizeTextNode(node.nextSibling);
  }

  /**
   * Marks a custom regular expression
   * @param {RegExp} regexp - The regular expression
   * @param {object} [opt] - Optional options object
   */
  markRegExp(regexp, opt) {
    this.opt = opt;
    this.log(`Searching with expression "${regexp}"`);

    // Reset the original search terms array
    this.opt._originalSearchTerms = [];

    // For RegExp searches, we still need to respect the separateWordSearch setting
    // when it comes to matching across elements
    const useAcrossElements = this.opt.separateWordSearch && this.opt.acrossElements;

    let totalMatches = 0;
    const fn = useAcrossElements
      ? "wrapMatchesAcrossElements"
      : "wrapMatches";
    const eachCb = (element) => {
      totalMatches++;
      this.opt.each(element);
    };
    this[fn](
      regexp,
      this.opt.ignoreGroups,
      (match, node) => this.opt.filter(node, match, totalMatches),
      eachCb,
      () => {
        if (totalMatches === 0) {
          this.opt.noMatch(regexp);
        }
        this.opt.done(totalMatches);
      }
    );
  }

  /**
   * Marks the specified search terms
   * @param {string|string[]} [sv] - Search value
   * @param {object} [opt] - Optional options object
   */
  mark(sv, opt) {
    this.opt = opt;

    // Reset the original search terms array
    this.opt._originalSearchTerms = [];

    let totalMatches = 0;

    // When separateWordSearch is false, we should not allow acrossElements
    const useAcrossElements = this.opt.separateWordSearch && this.opt.acrossElements;

    const fn = useAcrossElements
      ? "wrapMatchesAcrossElements"
      : "wrapMatches";

    // Convert search value to array if it's a string
    const searchTerms = typeof sv === "string" ? [sv] : sv;

    // Get the keywords based on separateWordSearch setting
    const { keywords: kwArr, length: kwArrLen } = this.getSeparatedKeywords(searchTerms);
    const sens = this.opt.caseSensitive ? "" : "i";

    const handler = (kw) => {
      // Store the original search term for reference
      this.opt._currentSearchTerm = kw;

      const regex = new RegExp(this.createRegExp(kw), `gm${sens}`);
      let matches = 0;
      this.log(`Searching with expression "${regex}"`);
      this[fn](
        regex,
        1,
        (_, node) => this.opt.filter(node, kw, totalMatches, matches),
        (element) => {
          matches++;
          totalMatches++;
          this.opt.each(element);
        },
        () => {
          if (matches === 0) {
            this.opt.noMatch(kw);
          }
          if (kwArr[kwArrLen - 1] === kw) {
            this.opt.done(totalMatches);
          } else {
            handler(kwArr[kwArr.indexOf(kw) + 1]);
          }
        }
      );
    };

    if (kwArrLen === 0) {
      this.opt.done(totalMatches);
    } else {
      handler(kwArr[0]);
    }
  }

  /**
   * Marks an array of objects containing a start with an end or length of the string to mark
   * @param {array} rawRanges - The original array of objects
   * @param {object} [opt] - Optional options object
   */
  markRanges(rawRanges, opt) {
    this.opt = opt;
    let totalMatches = 0;
    const ranges = this.checkRanges(rawRanges);
    if (ranges && ranges.length) {
      this.log(
        `Starting to mark with the following ranges: ${JSON.stringify(ranges)}`
      );
      this.wrapRangeFromIndex(
        ranges,
        (node, range, match, counter) =>
          this.opt.filter(node, range, match, counter),
        (element, range) => {
          totalMatches++;
          this.opt.each(element, range);
        },
        () => this.opt.done(totalMatches)
      );
    } else {
      this.opt.done(totalMatches);
    }
  }

  /**
   * Removes all marked elements inside the context with their HTML
   * @param {object} [opt] - Optional options object
   */
  unmark(opt) {
    this.opt = opt;
    let sel = this.opt.element ? this.opt.element : "*";
    sel += "[is-highlighty]";
    if (this.opt.className) {
      sel += `.${this.opt.className}`;
    }
    this.log(`Removal selector "${sel}"`);
    this.iterator.forEachNode(
      NodeFilter.SHOW_ELEMENT,
      (node) => this.unwrapMatches(node),
      (node) => {
        const matchesSel = DOMIterator.matches(node, sel);
        const matchesExclude = this.matchesExclude(node);
        return !matchesSel || matchesExclude
          ? NodeFilter.FILTER_REJECT
          : NodeFilter.FILTER_ACCEPT;
      },
      this.opt.done
    );
  }
}
