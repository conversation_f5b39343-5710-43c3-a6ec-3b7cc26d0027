import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Switch } from "@chakra-ui/react";
import { useEffect, useState } from "react";
import {
  getStorageDataLocal,
  setStorageDataLocal,
} from "../../../../chrome/chrome.utils";
import { AnalyticsData } from "../../../../chrome/features/content.analytics";
import useStateManager from "../../../../store/stateManager.hook";
import AnalyticElementsAccordion from "./AnalyticElementsAccordion";

import { colors } from "../../../../styles/global.styles";
import { exportCSV, receiveMessageFromContent } from "./analytics.utils";

function Analytics() {
  const { globalSettings } = useStateManager();

  const [analyticsData, setAnalyticsData] = useState<AnalyticsData[]>([
    // ...analyticMock,
  ]);

  const [defaultSlice, setDefaultSlice] = useState<number | undefined>(4);

  async function getAnalyticsFromStorage() {
    const data = (await getStorageDataLocal("analyticsData")) as {
      analyticsData: AnalyticsData[];
    };

    setAnalyticsData(data?.analyticsData);
  }

  useEffect(() => {
    receiveMessageFromContent(setAnalyticsData);

    return () => {};
  }, []);

  function showAllHistory() {
    setDefaultSlice(undefined);
  }

  function sliceStoredData(analyticsData: AnalyticsData[]) {
    const sliceData = analyticsData?.slice(0, defaultSlice);

    return sliceData;
  }

  function cleanAnalyticsFromStorage() {
    setStorageDataLocal<AnalyticsData[]>({
      analyticsData: [],
    });
    setAnalyticsData([]);
  }
  useEffect(() => {
    // if (analyticsData?.length) return () => {};

    getAnalyticsFromStorage();
    return () => {};
  }, []);

  function onChangeAnalytics() {
    return globalSettings.onSetIsAnalytics(!globalSettings?.analytics?.isOn);
  }

  return (
    <Flex flexDir={"column"}>
      <Flex justify={"space-around"}>
        <FormLabel
          my="auto"
          mr="auto"
          pos="relative"
          display="flex"
          fontSize={"12px"}
          pl="1"
          width="50px"
        >
          {globalSettings?.analytics?.isOn ? "On" : "Off"}
          <Switch
            ml="1"
            onChange={onChangeAnalytics}
            my="auto"
            size="sm"
            isChecked={globalSettings?.analytics?.isOn}
          />
        </FormLabel>

        <Button
          ml="auto"
          mr="1"
          my="1"
          _hover={
            !analyticsData?.length
              ? {}
              : {
                  bg: colors.brand.purple,
                  color: colors.brand.paragraphColor,
                }
          }
          _active={{
            bg: colors.brand.purpleFull,
            color: "white",
          }}
          bg={colors.brand.purpleFull}
          color="white"
          w="fit-content"
          size="xs"
          fontSize={"10px"}
          isDisabled={!analyticsData?.length}
          p="1"
          onClick={() => exportCSV(analyticsData)}
        >
          Export CSV
        </Button>
        <Button
          ml="auto"
          mr="1"
          my="1"
          colorScheme="blue"
          w="fit-content"
          size="xs"
          fontSize={"10px"}
          isDisabled={analyticsData?.length <= 4 || !defaultSlice}
          p="1"
          onClick={showAllHistory}
        >
          Show All
        </Button>
        <Button
          ml="auto"
          mr="1"
          my="1"
          colorScheme="red"
          w="fit-content"
          size="xs"
          fontSize={"10px"}
          p="1"
          isDisabled={!analyticsData}
          onClick={cleanAnalyticsFromStorage}
        >
          Clear History
        </Button>
      </Flex>
      {analyticsData ? (
        <AnalyticElementsAccordion
          analyticsData={sliceStoredData(analyticsData)}
        />
      ) : null}
    </Flex>
  );
}

export default Analytics;
