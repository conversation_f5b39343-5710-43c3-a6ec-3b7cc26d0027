import { useCallback, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  getStorageDataLocal,
  sendToAllTabs,
  setStorageDataLocal,
} from "../../../chrome/chrome.utils";
import {
  ExtensionViewState,
  GlobalSettings,
} from "../../../definitions/global.definition";
import {
  extensionViewInit,
  setExtensionViewSettings,
} from "../../../store/extensionView.slice";
import {
  initState,
  setGlobalSettings,
} from "../../../store/globalSettings.slice";
import { RootState } from "../../../store/index.store";
import useStateManager from "../../../store/stateManager.hook";
import { refreshSubscriptionStorage } from "../../MidPart/components/AccountTab/paddle.utils";
import { globalSettingsPopupInterceptor } from "../utils/popupStateInterceptor";

function useInitSettingView() {
  const dispatch = useDispatch();
  const isFirstRender = useRef(true);
  const globalSettings = useSelector(
    (state: RootState) => state.globalSettingsSlice
  );
  const extensionViewSettings = useSelector(
    (state: RootState) => state.extensionViewSlice
  );

  const {
    account: { isLoading, onSetIsAccountLoading },
    subscription: { subscription },
  } = useStateManager();

  const currentGlobalSettingsRef = useRef<any>(null);

  const initGlobalSettings = useCallback(async () => {
    try {
      const dataLocal = (await getStorageDataLocal("globalSettings")) as {
        globalSettings: GlobalSettings;
      };

      if (dataLocal?.globalSettings) {
        dispatch(setGlobalSettings(dataLocal.globalSettings));
        currentGlobalSettingsRef.current = dataLocal.globalSettings;
        // console.log('from db');
      } else {
        // console.log('from init');

        onSetIsAccountLoading(false); // if app has been opened for the first time, disregard account loading state

        dispatch(setGlobalSettings(initState));
        currentGlobalSettingsRef.current = dataLocal.globalSettings;
      }
    } catch (e) {
      // console.log(e, 'catch');
    }
  }, [dispatch, onSetIsAccountLoading]);

  const initExtensionViewSettings = useCallback(async () => {
    try {
      const extensionViewLocal = (await getStorageDataLocal(
        "extensionViewSettings"
      )) as {
        extensionViewSettings: ExtensionViewState;
      };

      const extensionView = extensionViewLocal;

      if (extensionView?.extensionViewSettings) {
        dispatch(
          setExtensionViewSettings(extensionView?.extensionViewSettings)
        );
      } else {
        dispatch(setExtensionViewSettings(extensionViewInit));
      }
    } catch (e) {
      // console.log(e, 'catch');
    }
  }, [dispatch]);

  useEffect(() => {
    if (isFirstRender.current && isLoading) {
      initGlobalSettings();
      initExtensionViewSettings();
      isFirstRender.current = false;
    }
    return () => {};
  }, [initGlobalSettings, initExtensionViewSettings, isLoading]);

  function deepEqual(obj1: any, obj2: any) {
    if (obj1 === obj2) return true;

    if (
      typeof obj1 !== "object" ||
      typeof obj2 !== "object" ||
      obj1 === null ||
      obj2 === null
    ) {
      return false;
    }

    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);

    if (keys1.length !== keys2.length) return false;

    for (const key of keys1) {
      if (!obj2.hasOwnProperty(key) || !deepEqual(obj1[key], obj2[key])) {
        return false;
      }
    }

    return true;
  }

  const saveGlobalSettings = useCallback(async () => {
    try {
      const globalSettingsData = globalSettingsPopupInterceptor(
        globalSettings,
        subscription?.isActive
      );
      const popupChangesMessage = {
        globalSettings: globalSettingsData,
        subscription: subscription,
      };
      const popupChangesSettingStorage = {
        globalSettings: globalSettingsData,
      };

      const hasSettingChanged = !deepEqual(
        currentGlobalSettingsRef.current,
        globalSettingsData
      );

      if (!hasSettingChanged) return;

      sendToAllTabs(popupChangesMessage);

      setStorageDataLocal<GlobalSettings>(popupChangesSettingStorage);
      // ez kell, mert a login es logout nem erzekeli a ui valtozasokat
      refreshSubscriptionStorage(subscription);

      currentGlobalSettingsRef.current = globalSettingsData;
    } catch (e) {}
  }, [globalSettings, subscription]);

  useEffect(() => {
    if (!isFirstRender.current && !isLoading && globalSettings) {
      // itt 1 renderrel kesik a globalSettings a saveGlobalSettings-hoz kepest, ezert kell a nerf function, hogy retriggerelje ezt a szart, de sajnos 2x fog lefutni bizonyos helyzetekben (signout,sigin)

      saveGlobalSettings();
    }
    return () => {};
  }, [globalSettings, saveGlobalSettings, subscription?.isActive, isLoading]);

  const saveExtensionViewSettings = useCallback(async () => {
    try {
      const data = {
        extensionViewSettings: extensionViewSettings,
      };

      setStorageDataLocal<ExtensionViewState>(data);
    } catch (e) {}
  }, [extensionViewSettings]);

  useEffect(() => {
    if (!isFirstRender.current && !isLoading) {
      saveExtensionViewSettings();
    }
    return () => {};
  }, [extensionViewSettings, saveExtensionViewSettings, isLoading]);

  return { isViewLoading: !globalSettings || !extensionViewSettings };
}

export default useInitSettingView;
