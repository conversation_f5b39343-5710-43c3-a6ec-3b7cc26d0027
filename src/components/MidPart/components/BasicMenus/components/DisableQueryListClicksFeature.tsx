import useStateManager from "../../../../../store/stateManager.hook";
import MenuDropdownItem from "../../MenuDropdownItem";

function DisableQueryListClicksFeature() {
  const { globalSettings } = useStateManager();

  return (
    <MenuDropdownItem
      onYes={() => globalSettings.onSetIsQueryClickDisabled(true)}
      onNo={() => globalSettings.onSetIsQueryClickDisabled(false)}
      isYes={globalSettings.isQueryClickDisabled?.isOn}
      label="Disable query box clicks"
      labelTooltip="Disable left and right click action on query boxes. Adds an extra layer of security against wild clicks. This way you cannot delete or edit the queries"
    />
  );
}

export default DisableQueryListClicksFeature;
