import {
    Flex,
    Slider,
    SliderFilledTrack,
    SliderThumb,
    SliderTrack,
    Tooltip
} from '@chakra-ui/react';
import { QueryItem } from '../../../definitions/global.definition';
import { sliderStyle } from '../../../styles/global.styles';
import { hslFormatter } from '../../shared/utils/color.utils';

interface Props {
  onQueryItemColorSlider: (colorValue: number) => void;
  onQueryItemOpacity: (opacityValue: number) => void;
  newItem: QueryItem;
}

function ColorSlider({
  onQueryItemColorSlider,
  newItem,
  onQueryItemOpacity,
}: Props) {
  return (
    <Flex flexDir={'column'}>
      <Slider
        {...sliderStyle.slider}
        onChange={onQueryItemColorSlider}
        mb="2"
        defaultValue={newItem.colors.hsl.h}
        value={newItem.colors.hsl.h}
        min={0}
        max={255}
        step={1}
      >
        <SliderTrack
          {...sliderStyle.track}
          bg={hslFormatter(newItem.colors.hsl)}
        />

        <Tooltip
          hasArrow
          fontSize={'10px'}
          color="white"
          placement="bottom"
          bg={hslFormatter(newItem.colors.hsl)}
          label={`hsl(${newItem.colors.hsl.h}, ${newItem.colors.hsl.s}%, ${newItem.colors.hsl.l}%, ${newItem.colors.hsl.a})`}
        >
          <SliderThumb {...sliderStyle.thumb} />
        </Tooltip>
      </Slider>

      <Flex align="center">
        <Slider
          {...sliderStyle.slider}
          onChange={onQueryItemOpacity}
          defaultValue={newItem.colors.hsl.a}
          value={newItem.colors.hsl.a}
          min={0.25}
          max={1}
          step={0.01}
        >
          <SliderTrack {...sliderStyle.track}>
            <SliderFilledTrack bg="brand.purple" />
          </SliderTrack>
          <Tooltip
            hasArrow
            fontSize={'10px'}
            color="white"
            placement="bottom"
            bg="brand.purpleFull"
            label={`Opacity: ${newItem.colors.hsl.a}%`}
          >
            <SliderThumb {...sliderStyle.thumb} />
          </Tooltip>
        </Slider>{' '}
      </Flex>
    </Flex>
  );
}

export default ColorSlider;
