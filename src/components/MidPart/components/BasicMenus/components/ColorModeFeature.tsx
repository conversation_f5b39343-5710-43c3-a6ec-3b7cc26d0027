import { Button, Flex, Icon, Text, useColorMode } from '@chakra-ui/react';
import { BsFillMoonFill, BsFillSunFill } from 'react-icons/bs';
import { menuButtonStyle } from '../../../../../styles/global.styles';

function ColorModeFeature() {
  const { colorMode, toggleColorMode } = useColorMode();

  return (
    <Flex
      rounded="md"
      transition="all 250ms"
      mb="1"
      flexDir="column"
      pt="3px"
      px="3px"
    >
      <Flex justify="space-between" align="center">
        <Flex pos="relative" align="center">
          <Text fontSize={'xs'}>Pop-up theme mode:</Text>
        </Flex>
        <Flex align="center">
          <Button
            onClick={toggleColorMode}
            bg={'unset'}
            _hover={{ bg: 'brand.purpleHover' }}
            _focus={{ bg: 'brand.purple' }}
            {...menuButtonStyle}
            w="28px"
          >
            {colorMode === 'dark' ? (
              <Icon as={BsFillSunFill} />
            ) : (
              <Icon as={BsFillMoonFill} />
            )}
          </Button>
        </Flex>
      </Flex>
    </Flex>
  );
}

export default ColorModeFeature;
