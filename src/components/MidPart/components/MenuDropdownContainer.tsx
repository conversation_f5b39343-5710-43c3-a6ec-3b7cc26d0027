import { Flex, SlideFade, TabPanel, Tab<PERSON>ane<PERSON>, Tabs } from "@chakra-ui/react";
import useStateManager from "../../../store/stateManager.hook";
import AccountTab from "./AccountTab/AccountTab";
import AdvancedMenus from "./AdvancedMenus/AdvancedMenus";
import Analytics from "./Analytics/Analytics";
import BasicMenus from "./BasicMenus/BasicMenus";
import ViewFeature from "./BasicMenus/components/MenuTabs";
import TutorialTextLink from "./TutorialTextLink";

function MenuDropdownContainer() {
  const {
    menuView: { menuView },
    menuDropdown,
  } = useStateManager();

  return (
    <SlideFade offsetY="14" in={menuDropdown.isMenuOpen} reverse>
      <Flex
        roundedBottom={"md"}
        roundedTop="xl"
        shadow="inset 0 0 5px 0 rgba(207, 206, 206, 0.75)"
        flexDir="column"
      >
        <TutorialTextLink />

        <Tabs index={menuView} defaultIndex={menuView}>
          <ViewFeature />
          <TabPanels>
            <TabPanel p="1">
              <BasicMenus />
            </TabPanel>
            <TabPanel p="1">
              <AdvancedMenus />
            </TabPanel>

            <TabPanel p="0">
              <Analytics />
            </TabPanel>

            <TabPanel p="1">
              <AccountTab />
            </TabPanel>
          </TabPanels>
        </Tabs>
      </Flex>
    </SlideFade>
  );
}

export default MenuDropdownContainer;
