import { Box, Tooltip } from "@chakra-ui/react";
import { cloneElement } from "react";
import useStateManager from "../../../../store/stateManager.hook";
import { tooltipStyle } from "../../../../styles/global.styles";

interface Props {
  children: any;
  label?: string;
  isOff?: boolean;
  isBgOff?: boolean;
  isNotRounded?: boolean;
  style?: Record<string, string | number>;
  isWithoutDisabled?: boolean;
}
function PremiumTooltip({
  isNotRounded,
  children,
  label,
  isOff,
  isBgOff,
  style,
  isWithoutDisabled,
}: Props) {
  const {
    subscription: { subscription },
  } = useStateManager();

  const labelText = label ? label : `This is a 🔐PRO feature.`;

  const withIsDisabled = (
    isWithoutDisabled
      ? {}
      : { isDisabled: subscription?.isActive ? false : true }
  ) as {};

  return isOff || subscription?.isActive ? (
    children
  ) : (
    <Tooltip
      {...tooltipStyle}
      bg={"brand.purpleFull"}
      textAlign="center"
      label={labelText}
      maxW="160px"
      cursor="not-allowed"
    >
      {isBgOff ? (
        cloneElement(children, withIsDisabled)
      ) : (
        <Box
          mb="2px"
          zIndex={2}
          rounded={isNotRounded ? "" : "md"}
          bg={"brand.purple"}
          {...style}
        >
          {cloneElement(children, withIsDisabled)}
        </Box>
      )}
    </Tooltip>
  );
}

export default PremiumTooltip;
