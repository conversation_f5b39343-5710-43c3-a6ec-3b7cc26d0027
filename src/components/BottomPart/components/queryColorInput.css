.chrome-picker {
  width: 200px !important;
  box-shadow: inset 0 0 2px 0.2px rgba(0, 0, 0, 0.26) !important;
  background: transparent !important;
  margin: auto;
  /* font-size: 5px !important; */
}

.chrome-picker > div:nth-of-type(2) {
  /* padding: 10px !important; */
  padding-top: 6px !important;
  padding-bottom: 4px !important;
}

/* AHOL A SLIDEREK VANNAK */

.chrome-picker > div:nth-of-type(2) > div:nth-of-type(1) {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}

.chrome-picker
  > div:nth-of-type(2)
  > div:nth-of-type(1)
  > div:nth-of-type(1)
  > div
  > div
  > div {
  border-radius: 0.75rem !important;
  height: 7px !important;
}

.chrome-picker
  > div:nth-of-type(2)
  > div:nth-of-type(1)
  > div:nth-of-type(2)
  > div
  > div
  > div {
  border-radius: 0.75rem !important;
  height: 7px !important;
}

.chrome-picker
  > div:nth-of-type(2)
  > div:nth-of-type(1)
  > div:nth-of-type(1)
  > div
  > div
  > div
  > div,
.chrome-picker
  > div:nth-of-type(2)
  > div:nth-of-type(1)
  > div:nth-of-type(2)
  > div
  > div
  > div
  > div {
  top: -2px !important;
}

/* AHOL A TEXT INPUTOK VANNAK */

.chrome-picker > div:nth-of-type(2) > div:nth-of-type(2) {
  padding-top: 4px !important;
  padding-bottom: 0px !important;
}
.chrome-picker
  > div:nth-of-type(2)
  > div:nth-of-type(2)
  > div:nth-of-type(1)
  > div
  > div
  > input {
  font-size: 9px !important;
  font-family: system-ui, sans-serif !important;
  font-weight: 500 !important;
}
.chrome-picker
  > div:nth-of-type(2)
  > div:nth-of-type(2)
  > div:nth-of-type(1)
  > div
  > div
  > label {
  font-size: 9px !important;
  margin-top: 4px !important;
  font-weight: 600 !important;
  font-family: system-ui, sans-serif !important;
}

/* border-radius: 0.75rem !important; */
