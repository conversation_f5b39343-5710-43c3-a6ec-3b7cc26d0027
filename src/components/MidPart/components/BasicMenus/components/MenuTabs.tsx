import { Tab, TabList, Text } from '@chakra-ui/react';
import useStateManager from '../../../../../store/stateManager.hook';
import PremiumTooltip from '../../../../shared/components/PremiumTooltip/PremiumTooltip';

function MenuTabs() {
  const {
    menuView: { onSetMenuView },
  } = useStateManager();

  return (
    <TabList w={'100%'} justifyContent="center">
      <Tab
        _selected={{
          color: 'brand.purpleFull',
          borderColor: 'brand.purpleFull',
        }}
        padding="0"
        mx="2"
        fontSize="12px"
        onClick={() => onSetMenuView(0)}
      >
        Basic
      </Tab>
      <Tab
        // _selected={{ color: 'brand.blueFull', borderColor: 'brand.blueFull' }}
        _selected={{
          color: 'brand.purpleFull',
          borderColor: 'brand.purpleFull',
        }}
        padding="0"
        mx="2"
        fontSize="12px"
        onClick={() => onSetMenuView(1)}
      >
        Advanced
      </Tab>

      <PremiumTooltip style={{ mb: 0 }}>
        <Tab
          _selected={{
            color: 'brand.purpleFull',
            borderColor: 'brand.purpleFull',
          }}
          padding="0"
          mx="1"
          fontSize="12px"
          onClick={() => onSetMenuView(2)}
        >
          <Text px="1">Analytics</Text>
        </Tab>
      </PremiumTooltip>
      <Tab
        bg="brand.blueFull"
        rounded="md"
        _selected={{
          bg: 'brand.purpleFull',
          color: 'white',
          borderColor: 'transparent',
        }}
        padding="0"
        mx="1"
        fontSize="12px"
        onClick={() => onSetMenuView(3)}
      >
        <Text fontWeight={600} px="1">
          Account
        </Text>
      </Tab>
    </TabList>
  );
}

export default MenuTabs;
