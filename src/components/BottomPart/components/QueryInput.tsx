import {
  Flex,
  Input,
  InputGroup,
  InputRightAddon,
  useOutsideClick,
} from "@chakra-ui/react";
import { ChangeEvent, Dispatch, useEffect, useRef, useState } from "react";
import { QueryItem } from "../../../definitions/global.definition";
import useStateManager from "../../../store/stateManager.hook";
import { colors, purpleInputStyle } from "../../../styles/global.styles";
import PremiumTooltip from "../../shared/components/PremiumTooltip/PremiumTooltip";
import { FREE_REGEXP_LENGTH } from "./QueryAdder";
import RegExpSwitch from "./RegExpSwitch";

interface Props {
  // setNewItem: React.Dispatch<React.SetStateAction<QueryItem>>;
  newItem: QueryItem;
  setNewItem: Dispatch<React.SetStateAction<QueryItem>>;
  onQueryItemName: (name: string) => void;
  onSubmitQuery: () => void;
}

const INIT_PLACEHOLDER = ".";

function QueryInput({
  onSubmitQuery,
  newItem,
  onQueryItemName,
  setNewItem,
}: Props) {
  const {
    inputFocus: { isInputFocus },
    subscription: { subscription },
    globalSettings: { isExtensionOn },
  } = useStateManager();
  const inputRef = useRef<HTMLInputElement>(null);

  const [isFocused, setIsFocused] = useState(false);
  const [placeholderDots, setPlaceholderDots] = useState(INIT_PLACEHOLDER);

  function onChangeInput(e: ChangeEvent<HTMLInputElement>) {
    if (
      !subscription?.isActive &&
      newItem.isRegexp &&
      e.target.value?.length === FREE_REGEXP_LENGTH + 1
    ) {
      return;
    }
    onQueryItemName(e.target.value);
  }

  function onFocus() {
    setIsFocused((state) => !state);
  }

  // DYNAMIC PLACEHOLDER

  useEffect(() => {
    let interval: NodeJS.Timer;
    if (isFocused || !newItem?.name?.length) {
      interval = setInterval(
        () =>
          setPlaceholderDots((state) => (state.length > 3 ? "." : state + ".")),
        600
      );
    }
    return () => {
      clearInterval(interval);
    };
  }, [isFocused, newItem]);

  useEffect(() => {
    if (inputRef.current && isInputFocus && isExtensionOn) {
      inputRef.current.focus();
      setIsFocused(true);
    }
  }, [isInputFocus, isExtensionOn]);

  useOutsideClick({
    ref: inputRef,
    handler: () => {
      setIsFocused(false);
      setPlaceholderDots(INIT_PLACEHOLDER);
    },
  });

  // DYNAMIC PLACEHOLDER

  return (
    <Flex flexDir="column" w="100%" justify="center" align="center">
      <RegExpSwitch newItem={newItem} setNewItem={setNewItem} />
      <InputGroup w="255px">
        <Input
          isDisabled={!isExtensionOn}
          ref={inputRef}
          {...purpleInputStyle}
          onChange={onChangeInput}
          // w=""
          padding="3px"
          size="sm"
          value={newItem.name}
          fontSize="12px"
          bg="white"
          placeholder={
            isFocused ? placeholderDots : "press Enter after each search query."
          }
          onClick={onFocus}
          borderRadius="md"
        />
        <PremiumTooltip
          label={"More than 5 queries are a 🔐PRO feature."}
          isBgOff
          isNotRounded
          isWithoutDisabled
        >
          <InputRightAddon
            onClick={onSubmitQuery}
            h="34px"
            w="35px"
            display={"flex"}
            justifyContent="center"
            children="+"
            cursor="pointer"
            bg={colors.brand.purple}
            marginTop="-1px"
            transition="all 300ms"
            ml="2px"
            border="unset"
          />
        </PremiumTooltip>
      </InputGroup>
    </Flex>
  );
}

export default QueryInput;
