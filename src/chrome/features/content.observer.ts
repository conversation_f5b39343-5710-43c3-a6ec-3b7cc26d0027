import { contentFileLogic } from "../content.utils";
import { currentGlobalSettings } from "./content.globalSettingsState";

// Constants for better readability
const DEBOUNCE_DELAY = 500;
const OBSERVER_DEBOUNCE_DELAY = 300;
const CHANGES_TO_REACT_TO = 2;

export const observerLogic = () => {
  const body = document.body;
  const isYoutube = window.location.href.includes("youtube");

  // Early return if conditions are not met
  if (!body || isYoutube || !currentGlobalSettings?.isExtensionOn) {
    return;
  }

  let timer: NodeJS.Timeout;
  let changeCounter = 0;
  const isObserver = true;

  // MutationObserver setup
  const observer = new MutationObserver((mutations: MutationRecord[]) => {
    mutations.forEach(() => {
      changeCounter += 1;

      if (changeCounter === CHANGES_TO_REACT_TO) {
        observer.disconnect();
        changeCounter = 0;

        if (timer) clearTimeout(timer);
        timer = setTimeout(() => {
          if (!currentGlobalSettings.isExtensionOn) return;

          contentFileLogic(currentGlobalSettings, isObserver);
        }, DEBOUNCE_DELAY);
      }
    });
  });

  // Debounce logic for events
  let debounceTimeoutId: NodeJS.Timeout;

  const handleEvent = () => {
    if (debounceTimeoutId) clearTimeout(debounceTimeoutId);
    debounceTimeoutId = setTimeout(() => {
      observer.observe(body, {
        childList: true,
        attributes: true,
        subtree: true,
      });
    }, OBSERVER_DEBOUNCE_DELAY);
  };

  // Attach event listeners
  const events = ["click", "keydown", "scroll", "mousemove"] as const;
  events.forEach((event) => window.addEventListener(event, handleEvent));
};
