import { Button, Flex } from '@chakra-ui/react';
import { Dispatch, FormEvent, useCallback, useEffect } from 'react';
import { ColorResult } from 'react-color';
import { v4 as uuidv4 } from 'uuid';
import { QueryItem } from '../../../definitions/global.definition';
import useStateManager from '../../../store/stateManager.hook';
import useCustomToast from '../../shared/components/Toast/CustomToast.hook';
import {
  convertHslToAll,
  getDefaultColors
} from '../../shared/utils/color.utils';
import QueryColorInput from './QueryColorInput';
import QueryDecorationInput from './QueryDecorationInput';
import QueryInput from './QueryInput';

interface Props {
  setNewItem: Dispatch<React.SetStateAction<QueryItem>>;
  setIsEdit: Dispatch<React.SetStateAction<boolean>>;
  newItem: QueryItem;
  isEdit: boolean;
}

export const FREE_REGEXP_LENGTH = 40;

/**
 * Helper function to handle decoration settings
 * Ensures consistent handling of decoration type and thickness
 */
const handleDecorationSettings = (
  item: QueryItem,
  decorationType?: string,
  decorationThickness?: string
): Pick<QueryItem, 'decorationType' | 'decorationThickness'> => {
  // Use provided values or existing values from the item
  const type = decorationType || item.decorationType || 'background';

  // Store the current thickness to preserve it when changing types
  const currentThickness = item.decorationThickness;

  // For background type, thickness should be undefined
  // For other types, use provided thickness, current thickness, or default to medium
  return {
    decorationType: type as QueryItem['decorationType'],
    decorationThickness: type !== 'background'
      ? (decorationThickness || currentThickness || 'medium') as QueryItem['decorationThickness']
      : undefined
  };
};

function QueryAdder({ setIsEdit, isEdit, newItem, setNewItem }: Props) {
  const customToast = useCustomToast();
  const {
    globalSettings: {
      onAddItemQueryList,
      onUpdateItemQueryList,
      queryListState,
    },
    minCharLimit: { isMinCharLimit },
    subscription: { subscription },
  } = useStateManager();

  const onQueryItemReset = useCallback(() => {
    setNewItem({
      name: '',
      colors: getDefaultColors(),
      id: uuidv4(),
      isInactive: false,
      isRegexp: false,
      decorationType: 'background',
      decorationThickness: 'medium',
    });
    setIsEdit(false);
  }, [setNewItem, setIsEdit]);

  useEffect(() => {
    if (!newItem.name.length) onQueryItemReset();

    return () => {};
  }, [newItem.name, onQueryItemReset]);

  function onSubmitQuery(e?: FormEvent) {
    e?.preventDefault();

    if (
      !newItem.name.length ||
      (isMinCharLimit && newItem?.name?.length <= 1)
    ) {
      return customToast(
        `Minimum ${isMinCharLimit ? '2 characters' : '1 character'}.`,
        'error'
      );
    }

    if (
      !subscription?.isActive &&
      newItem.name.length > FREE_REGEXP_LENGTH &&
      newItem.isRegexp
    ) {
      return customToast(
        `You've reached the ${FREE_REGEXP_LENGTH} character limit of Regexp with FREE account.`,
        'error'
      );
    }

    if (
      queryListState.selectedList.itemList.find(
        (query) => query.name.trim() === newItem.name.trim()
      ) &&
      !isEdit
    ) {
      return customToast(`Query is already in the list`, 'error');
    }

    if (
      queryListState.selectedList.itemList.length === 2 + 1 + 1 + 1 &&
      !subscription?.isActive
    ) {
      return customToast(`This requires a PRO account.`, 'error');
    }

    // Ensure decoration type and thickness are set using helper function
    const decorationSettings = handleDecorationSettings(newItem);

    const itemToSubmit = {
      ...newItem,
      ...decorationSettings
    };

    if (isEdit) {
      onUpdateItemQueryList(itemToSubmit);
    } else {
      onAddItemQueryList(itemToSubmit);
    }

    onQueryItemReset();
  }

  function onQueryItemName(name: string) {
    if (!name?.length) {
      // For new items, use default decoration settings
      setNewItem({
        name: name,
        colors: getDefaultColors(),
        id: uuidv4(),
        isInactive: false,
        isRegexp: false,
        decorationType: 'background',
        decorationThickness: 'medium',
      });
    } else {
      // For existing items, preserve decoration settings using helper function
      const decorationSettings = handleDecorationSettings(newItem);

      setNewItem({
        ...newItem,
        name: name,
        ...decorationSettings
      });
    }
  }

  function onQueryItemColorSlider(colorValue: number) {
    const newHsl = {
      ...newItem.colors.hsl,
      h: colorValue,
    };
    const colors = convertHslToAll(newHsl);
    setNewItem({ ...newItem, colors: colors });
  }
  function onQueryItemColorPicker(colorPickerValue: ColorResult) {
    const colors = {
      ...colorPickerValue,
      hsl: {
        h: colorPickerValue.hsl.h,
        s: parseInt((colorPickerValue.hsl.s * 100).toString(), 10),
        l: parseInt((colorPickerValue.hsl.l * 100).toString(), 10),
        a: colorPickerValue.hsl?.a,
      },
    };

    setNewItem({ ...newItem, colors: colors });
  }
  function onQueryItemColorPalette(colorPickerValue: ColorResult) {
    setNewItem({ ...newItem, colors: colorPickerValue });
  }
  function onQueryItemOpacity(opacityValue: number) {
    const newHsl = { ...newItem.colors.hsl, a: opacityValue };
    const colors = convertHslToAll(newHsl);

    setNewItem({ ...newItem, colors: colors });
  }
  function onQueryItemRandomColor() {
    setNewItem({ ...newItem, colors: getDefaultColors() });
  }

  function onQueryItemDecorationType(type: string) {
    // Use helper function to get consistent decoration settings
    const decorationSettings = handleDecorationSettings(newItem, type);

    setNewItem({
      ...newItem,
      ...decorationSettings
    });
  }

  function onQueryItemDecorationThickness(thickness: string) {
    // Use helper function to get consistent decoration settings
    const decorationSettings = handleDecorationSettings(
      newItem,
      newItem.decorationType,
      thickness
    );

    setNewItem({
      ...newItem,
      ...decorationSettings
    });
  }

  return (
    <Flex onSubmit={onSubmitQuery} as="form" align={'center'} flexDir="column">
      <QueryInput
        onQueryItemName={onQueryItemName}
        newItem={newItem}
        setNewItem={setNewItem}
        onSubmitQuery={onSubmitQuery}
      />
      <QueryColorInput
        onQueryItemRandomColor={onQueryItemRandomColor}
        onQueryItemColorSlider={onQueryItemColorSlider}
        onQueryItemColorPicker={onQueryItemColorPicker}
        onQueryItemColorPalette={onQueryItemColorPalette}
        newItem={newItem}
        onQueryItemOpacity={onQueryItemOpacity}
        onSubmitQuery={onSubmitQuery}
      />
      <QueryDecorationInput
        newItem={newItem}
        onQueryItemDecorationType={onQueryItemDecorationType}
        onQueryItemDecorationThickness={onQueryItemDecorationThickness}
        isEdit={isEdit}
      />
      <Button display={'none'} type="submit"></Button>
    </Flex>
  );
}

export default QueryAdder;
