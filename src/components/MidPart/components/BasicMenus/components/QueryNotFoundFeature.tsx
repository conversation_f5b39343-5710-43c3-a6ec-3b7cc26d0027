import useStateManager from '../../../../../store/stateManager.hook';
import MenuDropdownItem from '../../MenuDropdownItem';

function QueryNotFoundFeature() {
  const { globalSettings } = useStateManager();

  return (
    <MenuDropdownItem
      onYes={() => globalSettings.onHasQueryNotFound(true)}
      onNo={() => globalSettings.onHasQueryNotFound(false)}
      isYes={globalSettings.queryNotFound.isOn}
      label="Query not found alert"
    />
  );
}

export default QueryNotFoundFeature;
