import useStateManager from "../../../../../store/stateManager.hook";
import { disablePopupVisualFeatures } from "../../../../shared/utils/popupStateInterceptor";
import MenuDropdownItem from "../../MenuDropdownItem";

function FoundSizeIncreaseFeature({ isDisabled }: { isDisabled?: boolean }) {
  const { globalSettings } = useStateManager();

  return (
    <MenuDropdownItem
      onYes={() => globalSettings.onSetIsFoundSizeIncrease(true)}
      onNo={() => globalSettings.onSetIsFoundSizeIncrease(false)}
      isYes={disablePopupVisualFeatures(
        isDisabled,
        globalSettings.foundSizeIncrease.isOn
      )}
      label="Grow found query size"
      labelTooltip="Changes the found query font sizes to a fixed 25px"
      isFeatureDisabled={isDisabled}
    />
  );
}

export default FoundSizeIncreaseFeature;
