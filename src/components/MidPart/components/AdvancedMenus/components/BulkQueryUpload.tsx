import {
  Button,
  Flex,
  FormLabel,
  Icon,
  Input,
  Text,
  Tooltip,
} from '@chakra-ui/react';
import { useState } from 'react';
import { VscDebugRestart } from 'react-icons/vsc';
import readXlsxFile, { Row } from 'read-excel-file';
import { v4 as uuidv4 } from 'uuid';
import { QueryItem } from '../../../../../definitions/global.definition';
import useStateManager from '../../../../../store/stateManager.hook';
import { tooltipStyle } from '../../../../../styles/global.styles';
import useCustomToast from '../../../../shared/components/Toast/CustomToast.hook';
import { getDefaultColors } from '../../../../shared/utils/color.utils';
import MenuDropdownItem from '../../MenuDropdownItem';
import {
  limitFreeUpload,
  postLimitUploader,
  removeDuplicatesQueryList,
} from '../utils/queryUpload.utils';

type FROM_TO = Record<'from' | 'to' | 'column', string>;

const FROM_TO_DEFAULT = { column: '1', from: '1', to: '' } as FROM_TO;

function BulkQueryUpload() {
  const { globalSettings, subscription } = useStateManager();
  const customToast = useCustomToast();
  const [isLoading, setIsLoading] = useState(false);
  const [fromTo, setFromTo] = useState(FROM_TO_DEFAULT);
  const [uploadedSheet, setUploadedSheet] = useState<Row[]>([]);

  async function addExcelFile(e: React.ChangeEvent<HTMLInputElement>) {
    const file = e?.target?.files?.[0];

    if (!file) return customToast('File cannot be found.', 'error');

    try {
      const parsedExcel = await readXlsxFile(file);

      if (!parsedExcel.length)
        return customToast('No elements in the table.', 'error');

      setUploadedSheet(parsedExcel);
    } catch (error: any) {
      customToast(error.message, 'error');
    }
  }

  function createQueryItem(name: string): QueryItem {
    return {
      name: name,
      colors: getDefaultColors(),
      id: uuidv4(),
      isInactive: false,
      isRegexp: false,
      decorationType: 'background',
      decorationThickness: 'medium',
    };
  }

  function onFromToColumChange(e: React.ChangeEvent<HTMLInputElement>) {
    const currentInput = e.target.name;
    const currentValue = e.target.value?.length
      ? Math.abs(parseInt(e.target.value)).toString()
      : '';

    setFromTo((fromTo) => ({
      ...fromTo,
      [currentInput]: currentValue,
    }));
  }

  function removeDuplicatesFromList() {
    const column = fromTo?.column ? parseInt(fromTo?.column) - 1 : 0;

    const fromValue = fromTo?.from ? parseInt(fromTo.from) - 1 : 0;

    const toValue =
      !fromTo?.to.length || parseInt(fromTo?.to) === 0
        ? undefined
        : parseInt(fromTo?.to);

    const rowElements = uploadedSheet
      .map((item) => item[column] as string)
      .slice(fromValue, toValue);

    const duplicatesFilteredFromExcel = rowElements
      .filter((element, index) => {
        return rowElements.indexOf(element) === index;
      })
      .map((itemName) => createQueryItem(itemName));

    return duplicatesFilteredFromExcel;
  }

  async function processFile() {
    setIsLoading(true);
    const duplicatesFilteredFromExcel = removeDuplicatesFromList();

    const removedDuplicatesFromExistingList = removeDuplicatesQueryList(
      duplicatesFilteredFromExcel,
      globalSettings.queryListState.selectedList.itemList
    );

    if (!removedDuplicatesFromExistingList.length) {
      setIsLoading(false);

      return customToast('No new queries in the list.', 'error');
    }

    if (subscription?.subscription?.isActive) {
      globalSettings?.onUploadItemQueryList(removedDuplicatesFromExistingList);
      setIsLoading(false);
      customToast(
        `Uploaded ${removedDuplicatesFromExistingList?.length} queries.`,
        'success'
      );
    } else {
      const limitedUploadList = limitFreeUpload(
        removedDuplicatesFromExistingList,
        globalSettings.queryListState.selectedList.itemList.length
      );

      postLimitUploader(
        limitedUploadList,
        globalSettings?.onUploadItemQueryList,
        customToast
      );
      setIsLoading(false);
    }
  }

  return (
    <MenuDropdownItem
      isMore
      label="Import excel/sheets file"
      exclamationTooltip='High volume (>1000) may cause lagging in the browser.'
      labelTooltip="Upload query list from .xlsx (excel) file format. New queries expand the list, duplicate queries will be skipped. Colors will automatically be assigned."
    >
      <Text textAlign={'center'} fontSize={'10px'}>
        At a time, only one column is being parsed.
      </Text>
      <Flex w="100%" justify={'center'} flexDir="column" align="center">
        <Input
          name="file-upload"
          bg={'brand.menuBarBg'}
          rounded="md"
          p="1"
          h="auto"
          size="xs"
          type="file"
          onChange={addExcelFile}
        />
      </Flex>

      <Flex>
        <FormLabel
          w="100%"
          fontSize={'11px'}
          p="1"
          rounded="md"
          color="black"
          textAlign={'left'}
          display={'flex'}
          mx="auto"
          flexDir={'column'}
        >
          <Text>Column</Text>
          <Input
            placeholder="Column no."
            name="column"
            bg={'brand.menuBarBg'}
            rounded="md"
            h="24px"
            size="xs"
            type="number"
            value={fromTo?.column}
            onChange={onFromToColumChange}
            mx="auto"
          />
        </FormLabel>
        <FormLabel
          w="100%"
          fontSize={'11px'}
          p="1"
          rounded="md"
          color="black"
          textAlign={'left'}
          display={'flex'}
          flexDir={'column'}
          mx="auto"
        >
          <Text>From row</Text>
          <Input
            name="from"
            placeholder="from #"
            bg={'brand.menuBarBg'}
            rounded="md"
            h="24px"
            size="xs"
            type="number"
            value={fromTo?.from}
            onChange={onFromToColumChange}
          />
        </FormLabel>

        <FormLabel
          w="100%"
          fontSize={'11px'}
          p="1"
          rounded="md"
          color="black"
          textAlign={'left'}
          display={'flex'}
          flexDir={'column'}
          mx="auto"
        >
          <Text>To row</Text>
          <Input
            placeholder="to #"
            name="to"
            bg={'brand.menuBarBg'}
            rounded="md"
            h="24px"
            size="xs"
            type="number"
            value={fromTo?.to}
            onChange={onFromToColumChange}
          />
        </FormLabel>

        <Tooltip {...tooltipStyle} label="Reset numbers to default">
          <Button
            mt="5"
            onClick={() => setFromTo(FROM_TO_DEFAULT)}
            isDisabled={!fromTo?.from && !fromTo?.to}
            colorScheme="red"
            size="xs"
            ml="1"
          >
            <Icon as={VscDebugRestart} />
          </Button>
        </Tooltip>
      </Flex>

      <Button
        bg={'brand.blue'}
        _hover={{ bg: 'brand.blueFull' }}
        isLoading={isLoading}
        isDisabled={!uploadedSheet.length}
        size="xs"
        onClick={processFile}
        mx="auto"
        width="fit-content"
      >
        PROCESS
      </Button>
    </MenuDropdownItem>
  );
}

export default BulkQueryUpload;
