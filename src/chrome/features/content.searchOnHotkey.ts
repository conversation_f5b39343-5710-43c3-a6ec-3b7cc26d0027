import { debounce } from "../shared";
import { currentGlobalSettings } from "./content.globalSettingsState";
import { doMarkSearch } from "./content.highlight";
import { showFoundCountOnIcon } from "./content.sumBadge";
import { toastLogic } from "./content.toast";
let selectListener = null as unknown as AbortController;

export const initSearchOnHotkey = (isObserver?: boolean) => {
  const isSearchOnHotkey = currentGlobalSettings.isSeachOnHotkey.isOn;

  const selectPress = async (e: KeyboardEvent) => {
    if (e.key === "F" && isSearchOnHotkey) {
      // run search code
      runSearch(isObserver);
    }
  };

  if (isSearchOnHotkey && !selectListener) {
    selectListener = new AbortController();

    document.addEventListener("keyup", selectPress, selectListener);
  } else if (!isSearchOnHotkey && selectListener?.abort) {
    selectListener.abort();
    selectListener = null as unknown as AbortController;
  } else if (!isSearchOnHotkey) {
    runSearch(isObserver);
  }
};

const runSearch = (isObserver?: boolean) => {
  doMarkSearch(currentGlobalSettings, isObserver);

  const debouncedToastLogic = debounce(() => {
    toastLogic(currentGlobalSettings);
    showFoundCountOnIcon(currentGlobalSettings);
  }, 500);

  debouncedToastLogic();
};
