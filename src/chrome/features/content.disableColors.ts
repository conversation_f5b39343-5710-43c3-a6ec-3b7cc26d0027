import { GlobalSettings } from '../../definitions/global.definition';
import { cssHility, disableColorsState } from '../content.consts';
import { disableNavigationCss } from './content.navigation';

export const disableColorsOnKeyStroke = (
  globalSettings: GlobalSettings,
  isObserver?: boolean
) => {
  if (isObserver) return;

  const isTemporaryDeselect = globalSettings.temporaryDeselect.isOn;

  const allQueries = document.querySelectorAll(`[${cssHility}]`);

  if (!allQueries?.length) return;

  const keyDown = (e: KeyboardEvent) => {
    if (e.key === 'D' && e.shiftKey && isTemporaryDeselect) {
      disableColorsState.isStyleDisabled = !disableColorsState.isStyleDisabled;
      selectDeselect(disableColorsState.isStyleDisabled);
      disableNavigationCss(disableColorsState.isStyleDisabled);
    }
  };

  if (isTemporaryDeselect) {
    document.addEventListener('keydown', keyDown);
  } else {
    document.removeEventListener('keydown', keyDown, { capture: true });
  }

  const initialQueries = Array.from(allQueries);

  const selectDeselect = (isStyleDisabled: boolean) =>
    initialQueries.map(
      (styleSheet) => ((styleSheet as any).disabled = isStyleDisabled)
    );

  if (!isTemporaryDeselect && disableColorsState.isStyleDisabled) {
    disableColorsState.isStyleDisabled = false;
    selectDeselect(disableColorsState.isStyleDisabled);
    disableNavigationCss(disableColorsState.isStyleDisabled);

    return;
  }
};
