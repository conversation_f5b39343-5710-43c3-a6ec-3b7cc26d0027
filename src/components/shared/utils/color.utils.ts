import convert from "color-convert";
import { ColorResult, HSLColor, RGBColor } from "react-color";
import { best } from "wcag-color";

export function rgbRandomizer(): RGBColor {
  return {
    r: getRandomArbitrary(),
    g: getRandomArbitrary(),
    b: getRandomArbitrary(),
    a: 1,
  };
}
export function hslRandomizer(isBright?: boolean): HSLColor {
  return isBright
    ? {
        h: getRandomArbitrary(50, 350),
        s: getRandomArbitrary(85, 97),
        l: getRandomArbitrary(85, 95),
        a: 1,
      }
    : {
        h: getRandomArbitrary(0, 360),
        s: getRandomArbitrary(20, 80),
        l: getRandomArbitrary(30, 85),
        a: 1,
      };
}

export function convertRgbToAll(rgb: RGBColor): ColorResult {
  const hsl = convert.rgb.hsl(rgb.r, rgb.g, rgb.b);
  const hex = convert.rgb.hex(rgb.r, rgb.g, rgb.b);

  const result = {
    hex: hex,
    rgb: rgb,
    hsl: { h: hsl[0], s: hsl[1], l: hsl[2], a: rgb?.a },
  };

  return result;
}
export function convertHslToAll(hsl: HSLColor): ColorResult {
  const rgb = convert.hsl.rgb([hsl.h, hsl.s, hsl.l]);
  const hex = convert.hsl.hex([hsl.h, hsl.s, hsl.l]);

  const result = {
    hex: hex,
    rgb: { r: rgb[0], g: rgb[1], b: rgb[2], a: hsl?.a },
    hsl: hsl,
  };

  return result;
}

export function getRandomArbitrary(min = 0, max = 255) {
  const random = Math.random() * (max - min) + min;
  return parseInt(random.toString());
}

export function rgbFormatter({ rgb }: ColorResult) {
  return `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${rgb?.a})`;
}
export function hslFormatter(hsl: HSLColor) {
  return `hsla(${hsl.h}, ${hsl.s}%, ${hsl.l}%, ${hsl?.a})`;
}

export function getDefaultColors(isBright?: boolean): ColorResult {
  return convertHslToAll(hslRandomizer(isBright));
}

export function chooseColorPalette(hsl: HSLColor): ColorResult {
  return convertHslToAll(hsl);
}

export function generatePalette(): HSLColor[] {
  const hslNormal: HSLColor[] = Array.from(Array(10).keys()).map((_, i) => {
    return { h: i === 0 ? 10 : i * 35, s: 80, l: 85, a: 1 };
  });
  const hslOpacity: HSLColor[] = hslNormal.map((hslColors) => {
    return { ...hslColors, a: 0.5 };
  });

  return [...hslNormal, ...hslOpacity];
}

export function adaptiveFontColor(
  color: HSLColor,
  isContent?: boolean
): string {
  const colorString = `hsl(${color.h}, ${color.s}%, ${color.l}%)`;

  const whiteFontColor = "#FFFFFF"; // white,
  const darkFontColor = isContent ? "#000000" : "#61707d"; // brand.textColor

  const bestFontColor = best(whiteFontColor, darkFontColor, colorString);

  return (color?.a ?? 1) < 0.45 ? darkFontColor : bestFontColor;
}
