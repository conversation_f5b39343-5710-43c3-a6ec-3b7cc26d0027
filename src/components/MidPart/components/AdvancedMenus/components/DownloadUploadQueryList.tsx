import { Button, Flex, Input } from "@chakra-ui/react";
import { HSLColor } from "react-color";
import { v4 as uuidv4 } from "uuid";
import { QueryItem } from "../../../../../definitions/global.definition";
import useStateManager from "../../../../../store/stateManager.hook";
import useCustomToast from "../../../../shared/components/Toast/CustomToast.hook";
import { convertHslToAll } from "../../../../shared/utils/color.utils";
import MenuDropdownItem from "../../MenuDropdownItem";
import {
  limitFreeUpload,
  postLimitUploader,
  removeDuplicatesQueryList,
} from "../utils/queryUpload.utils";

const example = {
  name: "example",
  itemList: [
    {
      name: "example4",
      color: { h: 290, s: 27, l: 46, a: 1 },
      isRegexp: false,
    },
    { name: "example3", color: { h: 84, s: 50, l: 41, a: 1 }, isRegexp: true },
    {
      name: "example2",
      color: { h: 333, s: 66, l: 73, a: 1 },
      isRegexp: false,
    },
    {
      name: "example1",
      color: { h: 304, s: 52, l: 80, a: 1 },
      isRegexp: true,
    },
  ],
};
function DownloadUploadQueryList() {
  const { globalSettings, subscription } = useStateManager();
  const customToast = useCustomToast();

  function downloadJson(isExample?: boolean) {
    let downloadableJSON;
    let listName;
    if (isExample) {
      downloadableJSON = example;

      listName = example.name;
    } else {
      const mappedItemList =
        globalSettings.queryListState.selectedList.itemList.map((item) => ({
          name: item?.name,
          color: item?.colors.hsl,
          isRegexp: item?.isRegexp,
        }));
      listName = globalSettings.queryListState.selectedList.name;
      downloadableJSON = {
        name: listName,
        itemList: mappedItemList,
      };
    }

    const jsonString = `data:text/json;chatset=utf-8,${encodeURIComponent(
      JSON.stringify(downloadableJSON)
    )}`;
    const link = document.createElement("a");
    link.href = jsonString;
    link.download = `${listName}-query-list.json`;

    link.click();
  }

  function uploadJson(e: React.ChangeEvent<HTMLInputElement>) {
    const file = e?.target?.files?.[0];

    if (!file) return;

    file.text().then(function (text: any) {
      const result: {
        name: string;
        itemList: { name: string; color: HSLColor; isRegexp: boolean }[];
      } = JSON.parse(text);

      if (!result.itemList.length)
        return customToast("No queries in the list.", "error");

      const hasErrors = result?.itemList?.filter(
        (item: { name: string; color: HSLColor; isRegexp: boolean }) =>
          !item?.name?.length ||
          !item?.color?.h ||
          !item?.color?.s ||
          !item?.color?.l ||
          !item?.color?.a
      );

      if (hasErrors?.length)
        return customToast("Format error, please check the example.", "error");

      const convertedList = result.itemList?.map((item) =>
        createQueryItem(item)
      );

      const mappedUploadItems: QueryItem[] = removeDuplicatesQueryList(
        convertedList,
        globalSettings.queryListState.selectedList.itemList
      );

      if (!mappedUploadItems.length)
        return customToast("No new queries in the list.", "error");

      if (subscription?.subscription?.isActive) {
        globalSettings?.onUploadItemQueryList(mappedUploadItems);

        customToast(
          `Uploaded ${mappedUploadItems?.length} queries.`,
          "success"
        );
      } else {
        const limitedUploadList = limitFreeUpload(
          mappedUploadItems,
          globalSettings.queryListState.selectedList.itemList.length
        );

        postLimitUploader(
          limitedUploadList,
          globalSettings?.onUploadItemQueryList,
          customToast
        );
      }
    });
  }

  function createQueryItem(item: {
    name: string;
    color: HSLColor;
    isRegexp: boolean;
    decorationType?: QueryItem['decorationType'];
    decorationThickness?: QueryItem['decorationThickness'];
  }): QueryItem {
    return {
      name: item.name,
      colors: convertHslToAll(item.color),
      id: uuidv4(),
      isInactive: false,
      isRegexp: item?.isRegexp === undefined ? false : item.isRegexp,
      decorationType: item?.decorationType || 'background',
      decorationThickness: item?.decorationThickness || 'medium',
    };
  }

  return (
    <MenuDropdownItem
      isMore
      label="Load or save query list from file"
      labelTooltip="Download from/upload to the currently selected query list from previous lists. Uploading expands the list, duplicate queries will be skipped."
    >
      <Flex w="100%" justify={"center"} flexDir="column" align="center">
        <Button
          isDisabled={
            !globalSettings.queryListState.selectedList.itemList?.length
          }
          mb="2"
          size="xs"
          onClick={() => downloadJson(false)}
        >
          Download current list
        </Button>
        <Button mb="2" size="xs" onClick={() => downloadJson(true)}>
          Download example
        </Button>

        <Input
          name="file-upload"
          bg={"brand.menuBarBg"}
          rounded="md"
          p="1"
          h="auto"
          mb="2"
          size="xs"
          type="file"
          onChange={uploadJson}
        />
      </Flex>
    </MenuDropdownItem>
  );
}

export default DownloadUploadQueryList;
