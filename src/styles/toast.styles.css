@keyframes appear {
  from {
    opacity: 0;
    transform: translateY(-100px);
  }
  to {
    opacity: 1;
    transform: translateY(0px);
  }
}

@keyframes disappear {
  from {
    opacity: 1;
    transform: translateY(0px);
  }
  to {
    opacity: 0;
    transform: translateY(-100px);
  }
}

.appear {
  animation-name: appear;
}

.disappear {
  animation-name: disappear;
}

hility-success,
hility-error {
  position: fixed;
  display: flex;
  align-items: center;
  width: auto;
  min-width: 120px;
  height: 28px;
  max-height: 28px;
  -webkit-margin-start: auto;
  -webkit-margin-end: auto;
  border-radius: 0.5rem;
  color: white;
  padding: 8px;
  font-size: 13px;
  top: 15px;
  z-index: 9999;
  cursor: pointer;
  animation-duration: 0.8s;
  animation-fill-mode: forwards;
  overflow: hidden;
  max-width: 50%;
}

hility-success {
  background: #38a169;
  left: 20px;
}

hility-error {
  background: #e53e3e;
  right: 20px;
}
