import { Flex, Icon, useToast } from '@chakra-ui/react';
import { AiOutlineCheckCircle, AiOutlineInfoCircle } from 'react-icons/ai';
import { BiError } from 'react-icons/bi';

export type ToastTypesPopup = 'success' | 'error' | 'info';

function useCustomToast() {
  const toast = useToast();
  // const toastIdRef = useRef<any>(null);

  function myToast(message: string, type: ToastTypesPopup) {
    let setting: any = { icon: null, bg: null };

    switch (type) {
      case 'success':
        setting.icon = AiOutlineCheckCircle;
        setting.bg = 'green.500';
        break;
      case 'error':
        setting.icon = BiError;
        setting.bg = 'red.500';
        break;
      case 'info':
        setting.icon = AiOutlineInfoCircle;
        setting.bg = 'blue.500';
        break;
    }

    if (!toast.isActive(message))
      return toast({
        duration: 3000,
        position: 'top',
        id: message,
        render: () => (
          <Flex
            maxW="200px"
            w="auto"
            mx="auto"
            rounded="md"
            color="white"
            p={1}
            bg={setting.bg}
            fontSize={'12px'}
            align="center"
          >
            <Icon
              my="auto"
              mr="1"
              cursor="pointer"
              fontSize={'16px'}
              color="white"
              shadow="md"
              as={setting.icon}
            />
            {message}
          </Flex>
        ),
      });
  }

  return myToast;
}

export default useCustomToast;
