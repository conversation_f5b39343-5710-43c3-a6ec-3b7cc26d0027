import { Flex, Kbd, Tooltip } from "@chakra-ui/react";
import useStateManager from "../../../../../store/stateManager.hook";
import { kbdStyle, tooltipStyle } from "../../../../../styles/global.styles";
import { disablePopupVisualFeatures } from "../../../../shared/utils/popupStateInterceptor";
import MenuDropdownItem from "../../MenuDropdownItem";

function TemporaryDeselectFeature({ isDisabled }: { isDisabled?: boolean }) {
  const { globalSettings } = useStateManager();

  return (
    <MenuDropdownItem
      onYes={() => globalSettings.onSetIsTemporaryDeselect(true)}
      onNo={() => globalSettings.onSetIsTemporaryDeselect(false)}
      isYes={disablePopupVisualFeatures(
        isDisabled,
        globalSettings.temporaryDeselect.isOn
      )}
      isMore
      label="Quick disable hotkey"
      labelTooltip="Disable highlighting on found queries by pressing Shift + D. Only on the pressed webpage stays activated."
      isFeatureDisabled={isDisabled}
    >
      <Flex w="100%" justify={"center"} align="center">
        <Tooltip {...tooltipStyle} label="Deselect queries">
          <Kbd {...kbdStyle}>Shift + D</Kbd>
        </Tooltip>
      </Flex>
    </MenuDropdownItem>
  );
}

export default TemporaryDeselectFeature;
