{"name": "highlighty", "version": "2.3.5", "private": true, "dependencies": {"@chakra-ui/react": "^2.4.1", "@craco/craco": "^7.0.0", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@reduxjs/toolkit": "^1.9.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/firefox-webext-browser": "^120.0.3", "color-convert": "^2.0.1", "firebase": "^9.15.0", "framer-motion": "^7.6.7", "react": "^18.2.0", "react-color": "^2.19.3", "react-dom": "^18.2.0", "react-icons": "^4.6.0", "react-redux": "^8.0.5", "react-scripts": "5.0.1", "read-excel-file": "^5.6.1", "typescript": "^4.9.3", "uuid": "^9.0.0", "wcag-color": "^1.1.1", "web-vitals": "^3.1.0"}, "scripts": {"start": "env-cmd -f .env.test react-scripts start", "build:prod": "npm run remove-all && npm run build:chrome-prod && npm run build:firefox-prod", "build:test": "npm run remove-all && rm -rf firefox_build_test && npm run build:chrome-test && npm run build:firefox-test", "build:chrome-prod": "INLINE_RUNTIME_CHUNK=false DISABLE_ESLINT_PLUGIN=true env-cmd -f .env.prod craco build && mv build chrome_build_prod", "build:chrome-test": "npm run remove-all && INLINE_RUNTIME_CHUNK=false DISABLE_ESLINT_PLUGIN=true env-cmd -f .env.test craco build && mv build chrome_build_test", "watch:chrome-test": "nodemon --watch src --ext js,jsx,ts,tsx,css,scss --exec 'npm run build:chrome-test'", "build:firefox-prod": "INLINE_RUNTIME_CHUNK=false DISABLE_ESLINT_PLUGIN=true env-cmd -f .env.firefox-prod craco build && mv build firefox_build_prod", "build:firefox-test": "npm run remove-all && INLINE_RUNTIME_CHUNK=false DISABLE_ESLINT_PLUGIN=true env-cmd -f .env.firefox-test craco build && mv build firefox_build_test", "remove-all": "rm -rf chrome_build_test && rm -rf chrome_build_prod && rm -rf firefox_build_test && rm -rf firefox_build_prod", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/chrome": "^0.0.202", "@types/color-convert": "^2.0.0", "@types/jest": "^29.2.3", "@types/mark.js": "^8.11.8", "@types/node": "^18.11.9", "@types/react": "^18.0.25", "@types/react-color": "^3.0.6", "@types/react-dom": "^18.0.9", "@types/uuid": "^8.3.4", "copy-webpack-plugin": "^12.0.2", "env-cmd": "^10.1.0", "mark.js": "^8.11.1", "nodemon": "^3.1.9", "replace-in-file-webpack-plugin": "^1.0.6", "terser-webpack-plugin": "^5.3.6"}}