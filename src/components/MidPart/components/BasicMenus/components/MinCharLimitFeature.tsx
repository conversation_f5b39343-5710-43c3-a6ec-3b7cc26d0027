import useStateManager from "../../../../../store/stateManager.hook";
import MenuDropdownItem from "../../MenuDropdownItem";

function MinCharLimitFeature() {
  const {
    minCharLimit: { onIsMinCharLimit, isMinCharLimit },
  } = useStateManager();

  return (
    <MenuDropdownItem
      onYes={() => onIsMinCharLimit(true)}
      onNo={() => onIsMinCharLimit(false)}
      isYes={isMinCharLimit}
      label="Min 2 char. limit"
      labelTooltip="When enabled, the search input will allow only queries that are longer than 2 characters. This way we reduce the chances for performance drops."
    />
  );
}

export default MinCharLimitFeature;
