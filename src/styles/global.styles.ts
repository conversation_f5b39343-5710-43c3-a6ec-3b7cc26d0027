import { ThemeConfig, keyframes } from "@chakra-ui/react";
import { switchTheme } from "./Switch.style";

export const darkThemeConfig: ThemeConfig = {
  initialColorMode: "dark",
  useSystemColorMode: true,
};

export const alertAnimationKeyframes = keyframes`
0% { transform: translateY(0px); }
50% { transform: translateY(-5px); }  
100% { transform: translateY(0px); }

`;
export const alertAnimation = `${alertAnimationKeyframes} 1.5s infinite`;

export const brandTitleStyle = {
  letterSpacing: "8.5px",
  fontSize: "30px",
  fontWeight: 700,
  cursor: "default",
  fontFamily: "Phenoregu",
  display: "inline-block",
};
export const tooltipStyle = {
  rounded: "md",
  fontSize: "11px",
  hasArrow: true,
};

export const extensionBackground =
  "linear-gradient( 249deg, rgba(214, 253, 254, 0.6306897759103641) 0, #fafeff 50%, rgba(226, 235, 254, 0.4962359943977591) 100% )";

export const menuButtonStyle = {
  size: "xs",
  h: "18px",
  p: "1",
  fontWeight: 400,
};

export const paidButtonStyle = (isOff: boolean) => {
  return isOff
    ? {}
    : {
        bg: "brand.purple",
        _active: { bg: "brand.purple" },
        _hover: { bg: "brand.purple" },
      };
};
export const baseInputStyle = {
  size: "xs",
  border: "2px solid transparent",
  shadow: "0px 0px 0px 1px white",
  rounded: "md",
};

export const kbdStyle = { py: "1", fontSize: "11px", cursor: "default" };

export const blueInputStyle = {
  ...baseInputStyle,

  _hover: {
    border: "2px solid var(--chakra-colors-brand-inputBlueHover)",
    outline: "transparent",
  },
  _focus: {
    outline: "brand.inputBlueHover",
    border: "2px solid var(--chakra-colors-brand-inputBlueHover)",
    boxShadow: "0 0 2px #719ece",
  },
};
export const purpleInputStyle = {
  ...baseInputStyle,

  _hover: {
    border: "2px solid var(--chakra-colors-brand-inputPurpleHover)",
    outline: "transparent",
  },
  _focus: {
    outline: "brand.inputPurpleHover",
    border: "2px solid var(--chakra-colors-brand-inputPurpleHover)",
    boxShadow: "0 0 2px #719ece",
  },
};

export const colors = {
  brand: {
    menuBarBg: "rgba(103, 186, 211, 0.33)",
    purple: "rgb(172 137 245 / 30%)",
    purpleFull: "rgb(172 137 245)",
    purpleHover: "rgb(172 137 245 / 15%)",
    blue: "rgba(118, 244, 233, 0.3)",
    blueFull: "rgba(118, 244, 233)",
    blueHover: "rgba(118, 244, 233, 15%)",
    textColor: "#61707d",
    dropdownBg: "rgb(250 251 255 / 40%)",
    paragraphColor: "#53606b",
    noButtonColor: "#f47676",
    noButtonColorHover: "rgb(244 118 118 / 85%)",
    yesButtonColor: "rgb(137 245 166 / 82%)",
    yesButtonColorHover: "rgb(137 245 166 / 62%)",
    inputBlueHover: "hsl(176deg 80% 90%)",
    inputPurpleHover: "rgb(172 137 245 / 50%)",
  },
};

export const customTheme = {
  colors,
  darkThemeConfig,
  components: { Switch: switchTheme },
};

export const sliderStyle = {
  slider: {
    // w: '100%',
    w: "230px",
  },
  track: {
    h: "7px",
    rounded: "xl",
  },
  thumb: {
    boxSize: 3,
    border: "unset",
    shadow: "inset 0 0 2px 0.2px rgba(0, 0, 0, 0.26)",
    _selected: { shadow: "inset 0 0 2px 0.2px rgba(0, 0, 0, 0.6)" },
    _focus: { shadow: "inset 0 0 2px 0.2px rgba(0, 0, 0, 0.6)" },
  },
};
