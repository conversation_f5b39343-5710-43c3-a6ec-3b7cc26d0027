import { Divider, Flex } from "@chakra-ui/react";
import WhatsNew from "../shared/components/WhatsNew/WhatsNew";
import Restarter from "./components/Restarter";
import Title from "./components/Title";
import TrialMessage from "./components/TrialMessage";
import SystemMessages from "./components/SystemMessages";

function TopPart() {
  return (
    <Flex w="100%" flexDir={"column"}>
      <Title />
      <Divider
        mx="auto"
        bg="gray.900"
        opacity="0.15"
        rounded="xl"
        my="2"
        w="82%"
        h={"0.5px"}
      />

      <SystemMessages />
      <Restarter />
      <WhatsNew />
      <TrialMessage />
    </Flex>
  );
}

export default TopPart;
