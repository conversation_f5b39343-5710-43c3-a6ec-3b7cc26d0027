import { GlobalSettings } from '../../definitions/global.definition';

export function isWhiteListedSite(settings: GlobalSettings) {
  const sites = settings?.whitelistSites.websiteList.split(',');

  if (!sites?.length) return true;

  const currentWebsite = window?.location?.href;

  return settings?.whitelistSites.isOn
    ? sites.reduce((acc, curr) => {
        if (currentWebsite.includes(curr.trim())) return (acc = true);
        return acc;
      }, false)
    : true;
}
export function isBlackListedSite(settings: GlobalSettings) {
  const sites = settings?.blacklistSites.websiteList.split(',');

  if (!sites?.length) return false;

  const currentWebsite = window?.location?.href;

  return settings?.blacklistSites.isOn
    ? sites.reduce((acc, curr) => {
        if (currentWebsite.includes(curr.trim())) return (acc = true);
        return acc;
      }, false)
    : false;
}
