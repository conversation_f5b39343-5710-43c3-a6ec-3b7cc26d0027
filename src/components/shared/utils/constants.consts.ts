export const colorPaletteDefault = { isOn: false, isDynamic: false };
export const switchOffHotkeyDefault = { isOn: false };

export const paidFeatures = {
  globalSettings: {
    foundSizeIncrease: { isOn: false },
    badgeFoundSum: { isOn: false },
    temporaryDeselect: { isOn: false },
    searchBySelect: {
      isOn: false,
      isBright: true,
    },
    switchOffHotkey: switchOffHotkeyDefault,
    scrollbarHighlighter: { isOn: true },
  },
  extensionView: {
    colorPalette: colorPaletteDefault,
    isColorPickerView: false,
  },
};

export const HIGHLIGHTY_DOMAIN = "https://www.highlighty.app";
