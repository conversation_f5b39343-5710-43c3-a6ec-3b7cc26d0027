import { validateSender } from "./backround.utils";
import { MessageResponse } from "./content.definitions";
import { addBadgeText } from "./features/background.sumBadge";

chrome.runtime.onInstalled.addListener((details) => {
  // console.log('[background.js] onInstalled', details);
});

(chrome ?? browser).runtime?.onMessage?.addListener(
  (
    message,
    sender: chrome.runtime.MessageSender,
    response: MessageResponse
  ) => {
    const isValidated = validateSender(message, sender);

    if (isValidated) {
      addBadgeText(message?.message, sender.tab?.id);
    }

    try {
      response({ success: true });
      // console.log("✅ Response sent!");
    } catch (error) {
      console.error("❌ Error in background.ts messaging:", error);
      response({ success: false, error: (error as any).message });
    }
  }
);
