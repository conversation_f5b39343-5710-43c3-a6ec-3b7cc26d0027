import { useDispatch, useSelector } from "react-redux";
import {
  AccountSubscription,
  MenuView,
  QueryItem,
  UserAccount,
  UserSubscription,
} from "../definitions/global.definition";
import {
  setAccount,
  setAccountAndSubsription,
  setIsLoading,
  setSubscription,
} from "./accountSubscription.slice";

import {
  setHasClickedRestart,
  setIsAccessibilitySettings,
  setIsColorPicker,
  setIsDarkTheme,
  setIsInputFocus,
  setIsMenuOpen,
  setIsMinCharLimit,
  setIsToggleAccessibilitySettings,
  setIstoggleColorPalette,
  setIsToggleColorPicker,
  setIstoggleDynamicColorPalette,
  setIsToggleMenuOpen,
  setMenuView,
  setSystemMessagesDate,
} from "./extensionView.slice";
import {
  setAddItemQueryList,
  setAddQueryList,
  setBlacklisSitestState,
  setEditQueryList,
  setHasQueryFound,
  setHasQueryNotFound,
  setIsAnalytics,
  setIsBadgeFoundSum,
  setIsBlacklistSite,
  setIsCaseSens,
  setIsCompactView,
  setIsCompleteWordSearch,
  setIsDiacriticSens,
  setIsExtensionOn,
  setIsFoundSizeIncrease,
  setIsIframeSearch,
  setIsNavigationBar,
  setIsQueryClickDisabled,
  setIsQueryClickInverted,
  setIsScrollbarHighlighter,
  setIsSearchBySelect,
  setIsSearchBySelectIsBright,
  setIsSearchOnHotkey,
  setIsSplitSearch,
  setIsSwitchOffHotkey,
  setIsTemporaryDeselect,
  setIsWhitelistSite,
  setNavigationBarDownButton,
  setNavigationBarUpButton,
  setNavigationBarWord,
  setRemoveItemQueryList,
  setRemoveQueryList,
  setSelectQuertList,
  setUpdateItemQueryList,
  setUpdloadItemQueryList,
  setWhitelistSitesState,
  setQueryItemDecoration,
} from "./globalSettings.slice";
import { RootState } from "./index.store";

function useStateManager() {
  const dispatch = useDispatch();

  const {
    isMenuOpen,
    isColorPickerView,
    menuView,
    isInputFocus,
    isMinCharLimit,
    hasClickedRestart,
    colorPalette,
    systemMessageDate,
    isAccessibilitySettings,
    // isDarkTheme,
  } = useSelector((state: RootState) => state.extensionViewSlice);
  const globalSettings = useSelector(
    (state: RootState) => state.globalSettingsSlice
  );
  const { account, subscription, isLoading } = useSelector(
    (state: RootState) => state.accountSubscriptionSlice
  );

  function onSetAccount(account: UserAccount) {
    dispatch(setAccount(account));
  }
  function onSetIsAccountLoading(isLoading: boolean) {
    dispatch(setIsLoading(isLoading));
  }
  function onSetSubscription(subscription: UserSubscription) {
    dispatch(setSubscription(subscription));
  }
  function onSetAccountAndSubscription(
    accountSubscription: AccountSubscription
  ) {
    dispatch(setAccountAndSubsription(accountSubscription));
  }

  // DROPDOWN MENU
  function onToggleMenu() {
    dispatch(setIsToggleMenuOpen());
  }
  function onSetMenu(state: boolean) {
    dispatch(setIsMenuOpen(state));
  }

  // COLOR PICKER
  function onToggleColorPickerSettings() {
    dispatch(setIsToggleColorPicker());
  }
  function onToggleColorPalette() {
    dispatch(setIstoggleColorPalette());
  }
  function onToggleDynamicColorPalette() {
    dispatch(setIstoggleDynamicColorPalette());
  }
  function onSetColorPickerSettings(state: boolean) {
    dispatch(setIsColorPicker(state));
  }

  // Accessibility Settings
  function onToggleAccessibilitySettings() {
    dispatch(setIsToggleAccessibilitySettings());
  }
  function onSetAccessibilitySettings(state: boolean) {
    dispatch(setIsAccessibilitySettings(state));
  }

  function onSetMenuView(state: MenuView) {
    dispatch(setMenuView(state));
  }

  // GLOBAL SETTINGS

  function onIsExtensionOn() {
    dispatch(setIsExtensionOn());
  }
  function onIsDarkTheme() {
    dispatch(setIsDarkTheme());
  }

  function onSetIsBadgeFoundSum(value: boolean) {
    dispatch(setIsBadgeFoundSum(value));
  }
  function onSetIsCompactView(value: boolean) {
    dispatch(setIsCompactView(value));
  }
  function onSetIsQueryClickInverted(value: boolean) {
    dispatch(setIsQueryClickInverted(value));
  }
  function onSetIsQueryClickDisabled(value: boolean) {
    dispatch(setIsQueryClickDisabled(value));
  }
  function onSetIsAnalytics(value: boolean) {
    dispatch(setIsAnalytics(value));
  }
  function onSetIsSearchOnHotkey(value: boolean) {
    dispatch(setIsSearchOnHotkey(value));
  }

  function onSetIsScrollbarHighlighter(value: boolean) {
    dispatch(setIsScrollbarHighlighter(value));
  }
  function onSetIsSearchBySelect(value: boolean) {
    dispatch(setIsSearchBySelect(value));
  }

  function onSetIsSwitchOffHotkey(value: boolean) {
    dispatch(setIsSwitchOffHotkey(value));
  }
  function onSetIsSearchBySelectIsBright(value: boolean) {
    dispatch(setIsSearchBySelectIsBright(value));
  }
  function onSetIsTemporaryDeselect(value: boolean) {
    dispatch(setIsTemporaryDeselect(value));
  }
  function onSetIsFoundSizeIncrease(value: boolean) {
    dispatch(setIsFoundSizeIncrease(value));
  }
  function onHasQueryNotFound(value: boolean) {
    dispatch(setHasQueryNotFound(value));
  }
  function onHasQueryFound(value: boolean) {
    dispatch(setHasQueryFound(value));
  }
  function onIsMinCharLimit(value: boolean) {
    dispatch(setIsMinCharLimit(value));
  }
  function onSetHasClickedRestart() {
    dispatch(setHasClickedRestart());
  }
  function onIsInputFocus(value: boolean) {
    dispatch(setIsInputFocus(value));
  }
  function onIsCaseSens(value: boolean) {
    dispatch(setIsCaseSens(value));
  }
  function onIsDiacriticSens(value: boolean) {
    dispatch(setIsDiacriticSens(value));
  }
  function onIsSplitSearch(value: boolean) {
    dispatch(setIsSplitSearch(value));
  }
  function onIsCompleteWordSearch(value: boolean) {
    dispatch(setIsCompleteWordSearch(value));
  }
  function onsIsIframeSearch(value: boolean) {
    dispatch(setIsIframeSearch(value));
  }

  // NAVIGATION BAR

  function onNavigationBarUpButton(button: string) {
    dispatch(setNavigationBarUpButton(button));
  }
  function onNavigationBarDownButton(button: string) {
    dispatch(setNavigationBarDownButton(button));
  }
  function onNavigationBarWord(item: QueryItem | null) {
    dispatch(setNavigationBarWord(item));
  }

  function onIsNavigationBar(value: boolean) {
    dispatch(setIsNavigationBar(value));
  }

  // QUERY LIST
  function onAddQueryList(name: string) {
    dispatch(setAddQueryList(name));
  }
  function onSelectQueryList(id: string) {
    dispatch(setSelectQuertList(id));
  }
  function onRemoveQueryList() {
    dispatch(setRemoveQueryList());
  }
  function onEditQueryList(value: any) {
    dispatch(setEditQueryList(value));
  }
  function onAddItemQueryList(value: QueryItem) {
    dispatch(setAddItemQueryList(value));
  }
  function onUpdateItemQueryList(value: QueryItem) {
    dispatch(setUpdateItemQueryList(value));
  }
  function onUploadItemQueryList(value: QueryItem[]) {
    dispatch(setUpdloadItemQueryList(value));
  }

  function onRemoveItemQueryList(value: QueryItem) {
    dispatch(setRemoveItemQueryList(value));
  }

  // QUERY LIST

  /// BLACK WHITE LISTS
  function onIsWhiteListSite(value: boolean) {
    dispatch(setIsWhitelistSite(value));
  }
  function onSetWhiteListSites(websites: string) {
    dispatch(setWhitelistSitesState(websites));
  }

  function onIsBlackListSite(value: boolean) {
    dispatch(setIsBlacklistSite(value));
  }
  function onSetBlackListSites(websites: string) {
    dispatch(setBlacklisSitestState(websites));
  }
  function onSetSystemMessages() {
    dispatch(setSystemMessagesDate());
  }

  // DECORATION SETTINGS
  function onSetQueryItemDecoration(
    queryItemId: string,
    decorationType: string,
    decorationThickness?: string
  ) {
    // console.log(`onSetQueryItemDecoration: id=${queryItemId}, type=${decorationType}, thickness=${decorationThickness}`);
    dispatch(
      setQueryItemDecoration({
        queryItemId,
        decorationType,
        decorationThickness:
          decorationType !== "background"
            ? decorationThickness || "medium"
            : undefined,
      })
    );
  }
  /// BLACK WHITE LISTS

  return {
    account: {
      account,
      isLoading,
      onSetAccount,
      onSetIsAccountLoading,
      onSetAccountAndSubscription,
    },
    subscription: {
      subscription,
      onSetSubscription,
      onSetIsAccountLoading,
      onSetAccountAndSubscription,
    },
    menuDropdown: {
      isMenuOpen: isMenuOpen,
      onToggleMenu,
      onSetMenu,
    },
    colorPicker: {
      isColorPickerSettings: isColorPickerView,
      onToggleColorPickerSettings,
      onSetColorPickerSettings,
    },
    colorPalette: {
      isOn: colorPalette?.isOn,
      isDynamic: colorPalette?.isDynamic,
      onToggleColorPalette,
      onToggleDynamicColorPalette,
    },
    menuView: {
      menuView,
      onSetMenuView,
    },
    freshRestart: {
      hasClickedRestart: hasClickedRestart,
      onSetHasClickedRestart,
    },
    systemMessages: {
      systemMessageDate: systemMessageDate,
      onSetSystemMessages,
    },
    accessibility: {
      isAccessibilitySettings,
      onToggleAccessibilitySettings,
      onSetAccessibilitySettings,
    },
    minCharLimit: {
      isMinCharLimit: isMinCharLimit,
      onIsMinCharLimit,
    },
    inputFocus: {
      isInputFocus: isInputFocus,
      onIsInputFocus,
    },

    whiteList: { onIsWhiteListSite, onSetWhiteListSites },
    blackList: { onSetBlackListSites, onIsBlackListSite },
    globalSettings: {
      ...globalSettings,
      onIsExtensionOn,
      onIsDarkTheme,
      onHasQueryFound,
      onHasQueryNotFound,
      onIsInputFocus,
      onIsMinCharLimit,
      onIsCaseSens,
      onIsDiacriticSens,
      onIsSplitSearch,
      onIsCompleteWordSearch,
      onsIsIframeSearch,
      onIsNavigationBar,
      onNavigationBarUpButton,
      onNavigationBarDownButton,
      onRemoveItemQueryList,
      onAddQueryList,
      onRemoveQueryList,
      onEditQueryList,
      onAddItemQueryList,
      onUpdateItemQueryList,
      onSelectQueryList,
      onNavigationBarWord,
      onSetIsBadgeFoundSum,
      onSetIsFoundSizeIncrease,
      onSetIsTemporaryDeselect,
      onSetIsSearchBySelect,
      onSetIsSearchBySelectIsBright,
      onUploadItemQueryList,
      onSetIsSwitchOffHotkey,
      onSetIsAnalytics,
      onSetIsCompactView,
      onSetIsQueryClickInverted,
      onSetIsQueryClickDisabled,
      onSetIsSearchOnHotkey,
      onSetIsScrollbarHighlighter,
      onSetQueryItemDecoration,
    },
  };
}

export default useStateManager;
