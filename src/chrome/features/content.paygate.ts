import { paidFeatures } from "../../components/shared/utils/constants.consts";
import {
  GlobalSettings,
  PaddleSubscription,
  QueryListState,
  UserSubscription,
} from "../../definitions/global.definition";
import { getStorageDataLocal, setStorageDataLocal } from "../chrome.utils";

async function getSubsFromLocal() {
  const subscriptionStorageLocal = (await getStorageDataLocal(
    "subscription"
  )) as {
    subscription: PaddleSubscription;
  };

  const subscriptionStorage = subscriptionStorageLocal;

  // console.log(subscriptionStorage, 'subscriptionStorage');

  return subscriptionStorage?.subscription;
}

export async function isSubscriptionStillValid() {
  const globalTime = new Date();

  const subscription = await getSubsFromLocal();

  const periodEnd = subscription?.currentPeriodEnd
    ? new Date(subscription?.currentPeriodEnd)
    : "";

  const isStillValid = subscription
    ? periodEnd >= globalTime && subscription.isActive
    : false;

  await setStorageDataLocal({ isSubscriptionStillValid: isStillValid });

  return isStillValid;
}

export async function globalSettingsFreemiumConverter(
  globalSettings: GlobalSettings
) {
  const isActive = await isSubscriptionStillValid();

  const result = isActive
    ? globalSettings
    : mapFreeGlobalSettings(globalSettings);

  return result;
}

export function globalSettingsFreemiumConverterMessaging(
  globalSettings: GlobalSettings,
  subscription?: UserSubscription
) {
  const isActive = subscription?.isActive;

  const result = isActive
    ? globalSettings
    : mapFreeGlobalSettings(globalSettings);

  return result;
}

export function mapFreeGlobalSettings(globalSettings: GlobalSettings) {
  const queryListState: QueryListState = {
    ...globalSettings.queryListState,
    selectedList: {
      ...globalSettings.queryListState.selectedList,
      itemList: globalSettings.queryListState.selectedList.itemList.slice(0, 5),
    },
  };

  return {
    ...globalSettings,
    ...paidFeatures.globalSettings,
    queryListState: queryListState,
  };
}
