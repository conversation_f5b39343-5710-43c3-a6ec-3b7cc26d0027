import { GlobalSettings } from "../../definitions/global.definition";
import { dataHility } from "../content.consts";
import {
  FoundNotFoundListState,
  ToastListType,
  ToastType,
} from "../content.definitions";

export let cachedFoundNotFoundList: FoundNotFoundListState;

export const toastLogic = (globalSettings: GlobalSettings) => {
  const currentfoundNotFoundList = getFoundNotFoundElements(globalSettings);

  const queryList = handleFoundNotFoundChange(currentfoundNotFoundList);

  if (
    !queryList.foundItems ||
    !queryList.notFoundItems ||
    (!globalSettings?.queryFound.isOn && !globalSettings?.queryNotFound.isOn)
  )
    return;

  addToastStylesheet();

  singleToastRunnger(globalSettings, "foundItems", queryList.foundItems);
  singleToastRunnger(globalSettings, "notFoundItems", queryList.notFoundItems);
};

const singleToastRunnger = (
  globalSettings: GlobalSettings,
  toastListType: ToastListType,
  queryList: string[]
) => {
  const whichState =
    toastListType === "foundItems" ? "queryFound" : "queryNotFound";
  const whichToast: ToastType =
    toastListType === "foundItems" ? "hility-success" : "hility-error";

  if (!globalSettings?.[whichState]) return;

  initToast(queryList, whichToast);
};

export const initToast = (
  nameArray: string[],
  type: "hility-success" | "hility-error"
) => {
  const isSuccess = type === "hility-success";
  const emoticon = isSuccess ? " 👍 " : " 👎 ";

  if (!nameArray?.length) return;

  const runningToastInstances = document.getElementsByTagName(type);

  if (runningToastInstances?.length) {
    runningToastInstances[0].remove();
  }

  const body = document.body;

  const toast: HTMLElement = document.createElement(type);
  toast.setAttribute("id", type);
  toast.classList.add("appear");
  toast.innerHTML = `${emoticon} ${nameArray.join(emoticon)}`;
  // toast.innerHTML = `
  // <b style="margin-right: 3px;">
  // ${isSuccess ? "FOUND: " : "NOT FOUND: "}
  // </b>
  // ${nameArray.join(isSuccess ? " 👍 " : " 👎 ")}`;
  // ${nameArray.join(" 🎨 ")}`;
  // ${nameArray.join(" ✍🏼 ")}`;
  // ${nameArray.join(" ➖ ")}`;

  body.appendChild(toast);

  toast.addEventListener("click", () => {
    toasterLeaveAnimation(toast);
  });

  setTimeout(() => toasterLeaveAnimation(toast), 5000);

  setTimeout(() => {
    toast.remove();
  }, 5400);
};

const toasterLeaveAnimation = (toast: HTMLElement) => {
  toast.classList.add("disappear");
  toast.classList.remove("appear");
};
export const addToastStylesheet = () => {
  const doesExist = document.getElementById("hility-css");

  if (doesExist) return;

  const stylesheet = document.createElement("style");
  stylesheet.setAttribute("type", "text/css");
  stylesheet.setAttribute("id", "hility-css");

  // DO NOT MODIFY HERE
  stylesheet.textContent = toastStyle;

  document.head.appendChild(stylesheet);
};

const toastStyle = `
@keyframes appear {
  from {
    opacity: 0;
    transform: translateY(-100px);
  }
  to {
    opacity: 1;
    transform: translateY(0px);
  }
}

@keyframes disappear {
  from {
    opacity: 1;
    transform: translateY(0px);
  }
  to {
    opacity: 0;
    transform: translateY(-100px);
  }
}

.appear {
  animation-name: appear;
}

.disappear {
  animation-name: disappear;
}

hility-success,
hility-error {
  position: fixed;
  display: flex;
  align-items: center;
  width: auto;
  min-height: 28px;
  -webkit-margin-start: auto;
  -webkit-margin-end: auto;
  border-radius: 0.5rem;
  color: white;
  padding: 8px;
  font-size: 13px;
  top: 15px;
  z-index: 9999;
  cursor: pointer;
  animation-duration: 0.8s;
  animation-fill-mode: forwards;
  overflow: hidden;
  max-width: 45%;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.35);
  word-break: break-all;
}

hility-success {
  background: rgba(56, 161, 105, 0.97);
  left: 20px;
}

hility-error {
  background: rgba(229, 62, 62, 0.97);
  right: 20px;
}

`;

export const getFoundNotFoundElements = (globalSettings: GlobalSettings) => {
  const items = globalSettings.queryListState.selectedList.itemList;

  const list = items.reduce(
    (acc, curr) => {
      const element = document.querySelectorAll(
        `[${dataHility}='${curr?.id}']`
      );

      element?.length
        ? (acc.foundItems = [...acc.foundItems, curr.name])
        : (acc.notFoundItems = [...acc.notFoundItems, curr.name]);

      return acc;
    },
    {
      foundItems: [] as string[],
      notFoundItems: [] as string[],
    }
  );

  // console.log(list, 'getFoundNotFoundElements');

  return list;
};

const handleFoundNotFoundChange = (
  currentfoundNotFoundList: FoundNotFoundListState // mostani
): FoundNotFoundListState => {
  if (!cachedFoundNotFoundList) {
    // csak adja vissza az elso talalatot

    // console.log(currentfoundNotFoundList, 'currentfoundNotFoundList');
    // console.log(cachedFoundNotFoundList, 'cachedFoundNotFoundList');

    // console.log('init cached toast');
    cachedFoundNotFoundList = currentfoundNotFoundList;
    return currentfoundNotFoundList;
  }

  if (
    cachedFoundNotFoundList.foundItems.length !==
      currentfoundNotFoundList.foundItems.length ||
    cachedFoundNotFoundList.notFoundItems.length !==
      currentfoundNotFoundList.notFoundItems.length
  ) {
    // ha bekerult ujabb elem a popupban

    const newFoundItems = currentfoundNotFoundList?.foundItems?.filter(
      (item) => !cachedFoundNotFoundList.foundItems.includes(item)
    );

    const newNotFoundItems = currentfoundNotFoundList?.notFoundItems?.filter(
      (item) => !cachedFoundNotFoundList.notFoundItems.includes(item)
    );

    const newFoundNotFoundList = {
      foundItems: newFoundItems,
      notFoundItems: newNotFoundItems,
    };
    // console.log('update toast list');

    cachedFoundNotFoundList = currentfoundNotFoundList; // adja vissza a regi meg az uj szavakat is
    return newFoundNotFoundList; // csak azt adja vissza ami uj
  } else {
    // ha nincs valtozas nem kell notification

    // console.log('nincs lista valtozas');
    return { foundItems: [], notFoundItems: [] };
  }
};
