import { Button, Fade, Flex, Icon, Text } from '@chakra-ui/react';
import { KeyboardEvent } from 'react';
import { ChromePicker, ColorResult } from 'react-color';
import { CiSliderHorizontal } from 'react-icons/ci';
import { GiPerspectiveDiceSixFacesRandom } from 'react-icons/gi';
import { HiOutlineColorSwatch } from 'react-icons/hi';
import { IoColorPaletteOutline } from 'react-icons/io5';
import { QueryItem } from '../../../definitions/global.definition';
import useStateManager from '../../../store/stateManager.hook';
import {
  menuButtonStyle,
  paidButtonStyle
} from '../../../styles/global.styles';
import PremiumTooltip from '../../shared/components/PremiumTooltip/PremiumTooltip';
import ColorPalette from './ColorPalette';
import ColorSlider from './ColorSlider';

import './queryColorInput.css';

interface Props {
  onQueryItemColorPicker: (pickerValue: ColorResult) => void;
  onQueryItemColorPalette: (pickerValue: ColorResult) => void;
  onQueryItemColorSlider: (colorValue: number) => void;
  onQueryItemOpacity: (opacityValue: number) => void;
  onQueryItemRandomColor: () => void;
  newItem: QueryItem;
  onSubmitQuery: () => void;
}

function QueryColorInput({
  onQueryItemRandomColor,
  onQueryItemColorSlider,
  onQueryItemColorPicker,
  onQueryItemColorPalette,
  newItem,
  onQueryItemOpacity,
  onSubmitQuery,
}: Props) {
  const {
    colorPicker,
    colorPalette,
    subscription: { subscription },
  } = useStateManager();

  function isSubscriptionActive(isFeatureActive?: boolean): boolean {
    return (subscription?.isActive && isFeatureActive) as boolean;
  }

  function handleOnSubmitQuery(e: KeyboardEvent<HTMLDivElement>) {
    if (e.key === 'Enter') onSubmitQuery();
  }

  if (!newItem?.name?.length) return null;

  return (
    <Fade in={newItem?.name?.length > 0} style={{ transition: 'all 150ms' }}>
      <Flex
        rounded="md"
        borderColor="brand.purple"
        bg={'rgb(255, 255, 255, 70%)'}
        py="2"
        px="1"
        flexDir={'column'}
        w="240px"
        mx="auto"
        borderTopRadius={'unset'}
        onKeyDownCapture={handleOnSubmitQuery}
        shadow="sm"
      >
        <Flex w="100%" justify={'space-between'}>
          <PremiumTooltip
            isBgOff
            isOff={isSubscriptionActive(colorPicker.isColorPickerSettings)}
          >
            <Button
              fontSize={'16px'}
              mb="2"
              {...menuButtonStyle}
              {...paidButtonStyle(
                isSubscriptionActive(colorPicker.isColorPickerSettings)
              )}
              onClick={colorPicker.onToggleColorPickerSettings}
            >
              {isSubscriptionActive(colorPicker.isColorPickerSettings) ? (
                <Flex align="center">
                  <Text fontWeight={'500'} mr="1" fontSize="10px">
                    Slider
                  </Text>
                  <Icon as={CiSliderHorizontal} />
                </Flex>
              ) : (
                <Flex align="center">
                  <Text fontWeight={'500'} mr="2px" fontSize="10px">
                    Color Picker
                  </Text>
                  <Icon as={IoColorPaletteOutline} />
                </Flex>
              )}
            </Button>
          </PremiumTooltip>
          <PremiumTooltip
            isBgOff
            isOff={isSubscriptionActive(colorPicker.isColorPickerSettings)}
          >
            <Button
              fontSize={'16px'}
              mb="2"
              {...menuButtonStyle}
              {...paidButtonStyle(isSubscriptionActive(colorPalette.isOn))}
              onClick={colorPalette.onToggleColorPalette}
            >
              <Flex align="center">
                <Text fontWeight={'500'} mr="2px" fontSize="10px">
                  Palette
                </Text>
                <Icon fontSize="14px" as={HiOutlineColorSwatch} />
              </Flex>
            </Button>
          </PremiumTooltip>
          <Button mb="2" {...menuButtonStyle} onClick={onQueryItemRandomColor}>
            <Text fontWeight={'500'} mr="2px" fontSize="10px">
              Randomize
            </Text>{' '}
            <Icon as={GiPerspectiveDiceSixFacesRandom} />
          </Button>
        </Flex>
        {isSubscriptionActive(colorPicker.isColorPickerSettings) ? (
          <ChromePicker
            onChange={onQueryItemColorPicker}
            color={newItem.colors.rgb}
          />
        ) : (
          <ColorSlider
            onQueryItemColorSlider={onQueryItemColorSlider}
            onQueryItemOpacity={onQueryItemOpacity}
            newItem={newItem}
          />
        )}
        {isSubscriptionActive(colorPalette?.isOn) ? (
          <ColorPalette onQueryItemColorPalette={onQueryItemColorPalette} />
        ) : null}
      </Flex>
    </Fade>
  );
}

export default QueryColorInput;
