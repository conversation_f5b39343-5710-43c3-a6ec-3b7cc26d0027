import type { PayloadAction } from "@reduxjs/toolkit";
import { createSlice } from "@reduxjs/toolkit";
import { v4 as uuidv4 } from "uuid";
import { paidFeatures } from "../components/shared/utils/constants.consts";
import {
  GlobalSettings,
  QueryItem,
  QueryListItem,
  QueryListState,
} from "../definitions/global.definition";
const websiteLists = { isOn: false, websiteList: "" };

const queryListInit: QueryListItem = {
  name: "default",
  value: "default",
  id: "default",
  itemList: [] as QueryItem[],
};

const queryListState: QueryListState = {
  selectedList: queryListInit,
  queryList: [queryListInit],
};

const navigationBarInit = {
  isOn: false,
  hasWordFocus: false,
  focusedWord: null,
  assignedButtons: { upButtonKey: "W", downButtonKey: "S" },
};

export const initState = {
  isExtensionOn: true,
  //
  queryNotFound: { isOn: true },
  queryFound: { isOn: true },
  analytics: { isOn: false },
  navigationBar: navigationBarInit,
  //
  caseSens: { isOn: false },
  diacriticSens: { isOn: false },
  splitSearch: { isOn: false },
  completeWordSearch: { isOn: false },
  iframeSearch: { isOn: false },
  isCompactVIew: { isOn: false },
  isQueryClickInverted: { isOn: false },
  isQueryClickDisabled: { isOn: false },
  isSeachOnHotkey: { isOn: false },

  //
  queryListState: queryListState,
  ///
  whitelistSites: websiteLists,
  blacklistSites: websiteLists,

  ...paidFeatures.globalSettings,
};

export const globalSettingsSlice = createSlice({
  name: "globalSettingsSlice",
  initialState: null as unknown as GlobalSettings,
  reducers: {
    setGlobalSettings: (state, { payload }: PayloadAction<GlobalSettings>) =>
      (state = { ...initState, ...payload }),
    setIsExtensionOn: (state) => {
      state.isExtensionOn = !state.isExtensionOn;
    },
    setHasQueryNotFound: (state, { payload }: PayloadAction<boolean>) => {
      state.queryNotFound.isOn = payload;
    },
    setIsBadgeFoundSum: (state, { payload }: PayloadAction<boolean>) => {
      state.badgeFoundSum.isOn = payload;
    },
    setIsCompactView: (state, { payload }: PayloadAction<boolean>) => {
      state.isCompactVIew.isOn = payload;
    },
    setIsQueryClickInverted: (state, { payload }: PayloadAction<boolean>) => {
      state.isQueryClickInverted.isOn = payload;
    },
    setIsQueryClickDisabled: (state, { payload }: PayloadAction<boolean>) => {
      state.isQueryClickDisabled.isOn = payload;
    },
    setIsAnalytics: (state, { payload }: PayloadAction<boolean>) => {
      state.analytics.isOn = payload;
    },
    setIsSearchBySelect: (state, { payload }: PayloadAction<boolean>) => {
      state.searchBySelect.isOn = payload;
    },
    setIsSwitchOffHotkey: (state, { payload }: PayloadAction<boolean>) => {
      state.switchOffHotkey.isOn = payload;
    },
    setIsScrollbarHighlighter: (state, { payload }: PayloadAction<boolean>) => {
      if (!state.scrollbarHighlighter) {
        state.scrollbarHighlighter = { isOn: payload };
      } else {
        state.scrollbarHighlighter.isOn = payload;
      }
    },
    setIsSearchBySelectIsBright: (
      state,
      { payload }: PayloadAction<boolean>
    ) => {
      state.searchBySelect.isBright = payload;
    },
    setIsTemporaryDeselect: (state, { payload }: PayloadAction<boolean>) => {
      state.temporaryDeselect.isOn = payload;
    },
    setIsFoundSizeIncrease: (state, { payload }: PayloadAction<boolean>) => {
      state.foundSizeIncrease.isOn = payload;
    },
    setHasQueryFound: (state, { payload }: PayloadAction<boolean>) => {
      state.queryFound.isOn = payload;
    },
    setIsSearchOnHotkey: (state, { payload }: PayloadAction<boolean>) => {
      state.isSeachOnHotkey.isOn = payload;
    },

    // NAVIGATION BAR
    setIsNavigationBar: (state, { payload }: PayloadAction<boolean>) => {
      state.navigationBar.isOn = payload;
    },
    setNavigationBarWordFocus: (state) => {
      state.navigationBar.hasWordFocus = !state.navigationBar.hasWordFocus;
    },
    setNavigationBarWord: (
      state,
      { payload }: PayloadAction<QueryItem | null>
    ) => {
      state.navigationBar.focusedWord = payload;
    },
    setNavigationBarUpButton: (state, { payload }: PayloadAction<string>) => {
      state.navigationBar.assignedButtons.upButtonKey = payload;
    },
    setNavigationBarDownButton: (state, { payload }: PayloadAction<string>) => {
      state.navigationBar.assignedButtons.downButtonKey = payload;
    },
    // NAVIGATION BAR

    // ALLOWED WEBSITE
    setIsWhitelistSite: (state, { payload }: PayloadAction<boolean>) => {
      state.whitelistSites.isOn = payload;
    },
    setWhitelistSitesState: (state, { payload }: PayloadAction<string>) => {
      state.whitelistSites.websiteList = payload;
    },

    // ALLOWED WEBSITE

    // DISALLOWED WEBSITE
    setIsBlacklistSite: (state, { payload }: PayloadAction<boolean>) => {
      state.blacklistSites.isOn = payload;
    },
    setBlacklisSitestState: (state, { payload }: PayloadAction<string>) => {
      state.blacklistSites.websiteList = payload;
    },

    // DISALLOWED WEBSITE

    // QUERY LIST - LIST RELATED

    setSelectQuertList: (state, { payload }: PayloadAction<string>) => {
      state.queryListState.selectedList = state.queryListState.queryList.find(
        (listItem) => listItem.id === payload
      ) as QueryListItem;
    },

    setAddQueryList: (state, { payload }: PayloadAction<string>) => {
      const newItem = {
        name: payload,
        value: payload,
        id: uuidv4(),
        itemList: [] as any,
      };

      state.queryListState.selectedList = newItem;
      state.queryListState.queryList = [
        ...state.queryListState.queryList,
        newItem,
      ];

      state.navigationBar.focusedWord = null;
    },
    setRemoveQueryList: (state) => {
      const selectedList = state.queryListState.selectedList;
      if (selectedList.id === "default") {
        state.queryListState.selectedList = queryListInit;
        state.queryListState.queryList = state.queryListState.queryList.map(
          (item) =>
            item.id === state.queryListState.selectedList.id
              ? state.queryListState.selectedList
              : item
        );
      } else {
        state.queryListState.queryList = state.queryListState.queryList.filter(
          (list) => list.id !== state.queryListState.selectedList.id
        );
        state.queryListState.selectedList = state.queryListState.queryList[0];
      }
    },
    setEditQueryList: (state, { payload }: PayloadAction<string>) => {
      state.queryListState.selectedList.value = payload;
      state.queryListState.selectedList.name = payload;

      state.queryListState.queryList = state.queryListState.queryList.map(
        (item) =>
          item.id === state.queryListState.selectedList.id
            ? state.queryListState.selectedList
            : item
      );
    },
    // QUERY LIST - ITEM RELATED

    setAddItemQueryList: (state, { payload }: PayloadAction<QueryItem>) => {
      state.queryListState.selectedList.itemList = [
        payload,
        ...state.queryListState.selectedList.itemList,
      ];

      state.queryListState.queryList = state.queryListState.queryList.map(
        (item) =>
          item.id === state.queryListState.selectedList.id
            ? state.queryListState.selectedList
            : item
      );
    },
    setUpdateItemQueryList: (state, { payload }: PayloadAction<QueryItem>) => {
      state.queryListState.selectedList.itemList =
        state.queryListState.selectedList.itemList.map((item) =>
          item.id === payload.id ? payload : item
        );

      state.navigationBar.focusedWord =
        state.navigationBar.focusedWord?.id === payload.id
          ? payload
          : state.navigationBar.focusedWord;

      state.queryListState.queryList = state.queryListState.queryList.map(
        (item) =>
          item.id === state.queryListState.selectedList.id
            ? state.queryListState.selectedList
            : item
      );
    },
    setUpdloadItemQueryList: (
      state,
      { payload }: PayloadAction<QueryItem[]>
    ) => {
      state.queryListState.selectedList.itemList = [
        ...state.queryListState.selectedList.itemList,
        ...payload,
      ];

      state.queryListState.queryList = state.queryListState.queryList.map(
        (item) =>
          item.id === state.queryListState.selectedList.id
            ? state.queryListState.selectedList
            : item
      );
    },
    setRemoveItemQueryList: (state, { payload }: PayloadAction<QueryItem>) => {
      state.queryListState.selectedList.itemList =
        state.queryListState.selectedList.itemList.filter(
          (item) => item.id !== payload.id
        );

      state.navigationBar.focusedWord =
        state.navigationBar.focusedWord?.id === payload.id
          ? null
          : state.navigationBar.focusedWord;

      state.queryListState.queryList = state.queryListState.queryList.map(
        (item) =>
          item.id === state.queryListState.selectedList.id
            ? state.queryListState.selectedList
            : item
      );
    },

    // QUERY LIST

    setIsCaseSens: (state, { payload }: PayloadAction<boolean>) => {
      state.caseSens.isOn = payload;
    },
    setIsDiacriticSens: (state, { payload }: PayloadAction<boolean>) => {
      state.diacriticSens.isOn = payload;
    },
    setIsSplitSearch: (state, { payload }: PayloadAction<boolean>) => {
      state.splitSearch.isOn = payload;
    },
    setIsCompleteWordSearch: (state, { payload }: PayloadAction<boolean>) => {
      state.completeWordSearch.isOn = payload;
    },
    setIsIframeSearch: (state, { payload }: PayloadAction<boolean>) => {
      state.iframeSearch.isOn = payload;
    },

    setQueryItemDecoration: (
      state,
      {
        payload,
      }: PayloadAction<{
        queryItemId: string;
        decorationType: string;
        decorationThickness?: string;
      }>
    ) => {
      const { queryItemId, decorationType, decorationThickness } = payload;
      //   console.log(`setQueryItemDecoration action: id=${queryItemId}, type=${decorationType}, thickness=${decorationThickness}`);

      const queryList = state.queryListState.selectedList;
      const queryItem = queryList.itemList.find((qi) => qi.id === queryItemId);

      if (queryItem) {
        queryItem.decorationType =
          decorationType as QueryItem["decorationType"];

        // Set thickness only for non-background decorations
        if (decorationType !== "background") {
          queryItem.decorationThickness = (decorationThickness ||
            "medium") as QueryItem["decorationThickness"];
        } else {
          queryItem.decorationThickness = undefined;
        }

        //  console.log(`Updated queryItem:`, JSON.stringify(queryItem));
      } else {
        //    console.log(`QueryItem with id ${queryItemId} not found`);
      }
    },
  },
});

// Action creators are generated for each case reducer function
export const {
  setGlobalSettings,
  setIsExtensionOn,
  setIsAnalytics,
  setHasQueryFound,
  setHasQueryNotFound,
  setIsCaseSens,
  setIsDiacriticSens,
  setIsSplitSearch,
  setIsCompleteWordSearch,
  setIsIframeSearch,
  setIsNavigationBar,
  setNavigationBarWordFocus,
  setNavigationBarWord,
  setNavigationBarUpButton,
  setNavigationBarDownButton,
  setIsWhitelistSite,
  setWhitelistSitesState,
  setIsBlacklistSite,
  setBlacklisSitestState,
  setIsBadgeFoundSum,
  setIsFoundSizeIncrease,
  setIsTemporaryDeselect,
  setIsSearchBySelect,
  setIsSearchBySelectIsBright,
  setIsSwitchOffHotkey,
  setIsCompactView,
  setIsQueryClickInverted,
  setIsQueryClickDisabled,
  setIsSearchOnHotkey,
  setIsScrollbarHighlighter,
  /// QUERY LIST
  setAddQueryList,
  setRemoveQueryList,
  setEditQueryList,
  setAddItemQueryList,
  setUpdateItemQueryList,
  setRemoveItemQueryList,
  setSelectQuertList,
  setUpdloadItemQueryList,
  setQueryItemDecoration,
} = globalSettingsSlice.actions;

export default globalSettingsSlice.reducer;
