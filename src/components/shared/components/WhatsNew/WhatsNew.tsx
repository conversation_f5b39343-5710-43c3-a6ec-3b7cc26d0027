import {
  Box,
  Button,
  Fade,
  Flex,
  Heading,
  Icon,
  ListItem,
  Modal,
  ModalOverlay,
  UnorderedList,
} from "@chakra-ui/react";
import { useCallback, useEffect, useState } from "react";
import { AiOutlineClose } from "react-icons/ai";
import { getStorageDataLocal } from "../../../../chrome/chrome.utils";
import { WhatsNew } from "../../../../definitions/global.definition";
import { colors } from "../../../../styles/global.styles";
import { saveWhatsNew, whatsNewCurrent } from "./versionControl.consts";

// CSS for WhatsNew links
const whatsNewLinkStyles = `
  .whats-new-content a {
    color: ${colors.brand.purple};
    text-decoration: underline;
    transition: all 0.2s ease;
  }
  .whats-new-content a:hover {
    color: ${colors.brand.purpleHover};
    text-decoration: underline;
  }
`;

function WhatsNewReleaseNotes() {
  const [whatsNewState, setWhatsNewState] = useState<WhatsNew | null>(null);

  const [isOpen, setIsOpen] = useState(true);

  function onClose() {
    setIsOpen(false);
    saveWhatsNew();
  }

  const getWhatsNew = useCallback(async () => {
    const whatsNewOldLocal = (await getStorageDataLocal("whatsNew")) as {
      whatsNew: WhatsNew;
    };

    // console.log("WhatsNew: Current version:", whatsNewCurrent.version);
    // console.log("WhatsNew: Stored data:", whatsNewOldLocal);

    // Case 1: First installation or no previous version stored
    if (!whatsNewOldLocal?.whatsNew) {
      console.log("WhatsNew: First installation, showing popup");
      setWhatsNewState(whatsNewCurrent);
      setIsOpen(true);
      return;
    }

    // Case 2: New version is available
    if (whatsNewCurrent.version !== whatsNewOldLocal.whatsNew.version) {
      // console.log("WhatsNew: New version detected, showing popup");
      setWhatsNewState(whatsNewCurrent);
      setIsOpen(true);
      return;
    }

    // console.log("WhatsNew: No new version, not showing popup");
  }, []);

  useEffect(() => {
    getWhatsNew();
    return () => {};
  }, [getWhatsNew]);

  if (!isOpen || !whatsNewState) return null;

  return (
    <>
      {/* Add the CSS for styling links */}
      <Box
        as="style"
        dangerouslySetInnerHTML={{ __html: whatsNewLinkStyles }}
      />

      <Fade in={isOpen} style={{ transition: "all 100ms", zIndex: 999 }}>
        <Flex
          zIndex={"2"}
          position={"absolute"}
          w="240px"
          my="auto"
          top="10%"
          mb="10%"
          bg="white"
          shadow="lg"
          rounded="lg"
          flexDir={"column"}
          mx="15px"
          p="2"
          className="whats-new-content"
        >
          <Flex align="flex-start" justify="space-between">
            <Heading fontWeight={600} mb="2" fontSize="14px">
              What's new?
            </Heading>
            <Icon
              fontSize="15px"
              transition="all 100ms"
              _hover={{ color: "brand.blueFull" }}
              as={AiOutlineClose}
              cursor={"pointer"}
              onClick={onClose}
            />
          </Flex>

          <Flex flexDir={"column"} mt="1">
            <Heading textDecor={"underline"} fontWeight={400} fontSize="11px">
              version {whatsNewState?.version}:
            </Heading>

            <UnorderedList my="2">
              {whatsNewState.now.map((item, i: number) => (
                <ListItem key={i} fontSize={"11px"}>
                  <div dangerouslySetInnerHTML={{ __html: item }} />
                </ListItem>
              ))}
            </UnorderedList>

            {whatsNewState?.future?.length ? (
              <>
                <Heading
                  textDecor={"underline"}
                  fontWeight={400}
                  fontSize="11px"
                >
                  future release:
                </Heading>

                <UnorderedList my="2">
                  {whatsNewState.future.map((item, i: number) => (
                    <ListItem key={i} fontSize={"11px"}>
                      <div dangerouslySetInnerHTML={{ __html: item }} />
                    </ListItem>
                  ))}
                </UnorderedList>
              </>
            ) : null}
          </Flex>

          <Flex justify={"flex-end"} w="100%">
            <Button
              fontSize={"11px"}
              bg={colors.brand.purple}
              _hover={{
                bg: colors.brand.purpleHover,
              }}
              _active={{
                bg: colors.brand.purple,
              }}
              mr="2"
              size="xs"
              onClick={onClose}
            >
              Ok, gotcha!
            </Button>
          </Flex>
        </Flex>
      </Fade>
      <Modal
        blockScrollOnMount={false}
        isOpen={isOpen}
        onClose={() => undefined}
      >
        <ModalOverlay zIndex={"998"} />
      </Modal>
    </>
  );
}

export default WhatsNewReleaseNotes;
