import useStateManager from '../../store/stateManager.hook';
import MenuBar from './components/MenuBar';
import MenuDropdownContainer from './components/MenuDropdownContainer';
import MidWrapper from './components/MidWrapper';

function MidPart() {
  const { menuDropdown } = useStateManager();
  return (
    <MidWrapper>
      <MenuBar />
      {menuDropdown.isMenuOpen ? <MenuDropdownContainer /> : null}
    </MidWrapper>
  );
}

export default MidPart;
