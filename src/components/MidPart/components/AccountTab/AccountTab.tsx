import { Flex, Skeleton } from "@chakra-ui/react";

import useStateManager from "../../../../store/stateManager.hook";
import LoggedIn from "./LoggedIn";
import Login from "./Login";

function AccountTab() {
  const {
    account: { account, isLoading: isAccountLoading },
  } = useStateManager();

  return (
    <Skeleton
      startColor="brand.purpleFull"
      endColor="brand.blue"
      fadeDuration={2}
      isLoaded={!isAccountLoading}
    >
      <Flex flexDir={"column"}>{account ? <LoggedIn /> : <Login />}</Flex>
    </Skeleton>
  );
}

export default AccountTab;
