import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Box,
} from "@chakra-ui/react";

interface Props {
  children: JSX.Element | JSX.Element[];
  title: string;
}

function FeatureGroupingAccordion({ children, title }: Props) {
  return (
    <Accordion allowToggle>
      <AccordionItem>
        <AccordionButton
          _hover={{ bg: "brand.blueHover" }}
          _expanded={{ bg: "brand.purpleHover" }}
          p={1}
        >
          <Box
            fontWeight={500}
            fontSize={"12px"}
            as="span"
            flex="1"
            textAlign="left"
          >
            {title}
          </Box>
          <AccordionIcon />
        </AccordionButton>

        <AccordionPanel p="0">{children}</AccordionPanel>
      </AccordionItem>
    </Accordion>
  );
}

export default FeatureGroupingAccordion;
