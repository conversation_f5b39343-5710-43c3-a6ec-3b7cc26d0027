import { Grid, GridItem } from '@chakra-ui/react';
import { ColorResult } from 'react-color';
import { convertHslToAll, generatePalette, hslFormatter } from '../../shared/utils/color.utils';

interface Props {
  onQueryItemColorPalette: (pickerValue: ColorResult) => void;
}

function ColorPalette({ onQueryItemColorPalette }: Props) {
  const staticColorPalette = generatePalette();

  function onChooseColor(i: number) {
    const color = staticColorPalette[i];
    const colorConverted = convertHslToAll(color);
    onQueryItemColorPalette(colorConverted);
  }

  return (
    <Grid
      templateColumns="repeat(10, 1fr)"
      templateRows="repeat(2, 1fr)"
      w="100%"
      mt="3"
    >
      {staticColorPalette.map((color, i: number) => (
        <GridItem
          key={i}
          onClick={() => onChooseColor(i)}
          border="1px solid"
          transition={'all 150ms'}
          borderColor={'white'}
          _hover={{ borderColor: 'brand.purple' }}
          w="auto"
          h="5"
          bg={hslFormatter(color)}
          cursor="pointer"
        />
      ))}
    </Grid>
  );
}

export default ColorPalette;
