import { v4 as uuidv4 } from 'uuid';
import { getDefaultColors } from '../../components/shared/utils/color.utils';
import { GlobalSettings, QueryItem } from '../../definitions/global.definition';
import { setStorageDataLocal } from '../chrome.utils';
import {
  currentGlobalSettings,
  setGlobalSettings
} from './content.globalSettingsState';
import { doMarkSearch } from './content.highlight';
let selectListener = null as unknown as AbortController;

export const initSearchBySelect = () => {
  const isSearchBySelect = currentGlobalSettings.searchBySelect.isOn;
  const isSearchColorBright = currentGlobalSettings.searchBySelect.isBright;

  const selectPress = async (e: KeyboardEvent) => {
    if (e.key === 'L' && isSearchBySelect) {
      const selectedWord = window.getSelection()?.toString()?.trim();

      if (!selectedWord?.length) return;

      const newItem: QueryItem = {
        colors: getDefaultColors(isSearchColorBright),
        id: uuidv4(),
        isInactive: false,
        isRegexp: false,
        name: selectedWord,
        decorationType: 'background',
        decorationThickness: 'medium',
      };

      const isAlreadyInList =
        currentGlobalSettings.queryListState.selectedList.itemList.filter(
          (listItem) => listItem.name === newItem.name
        );

      if (isAlreadyInList?.length) return;

      const newGlobalSettings = mapGlobalSettings(newItem);

      saveToDatabase(newGlobalSettings);
      // await
      doMarkSearch(newGlobalSettings, undefined, newItem);
      setGlobalSettings(newGlobalSettings);
    }
  };

  if (isSearchBySelect && !selectListener) {
    selectListener = new AbortController();

    document.addEventListener('keyup', selectPress, selectListener);
  } else if (!isSearchBySelect && selectListener?.abort) {
    selectListener.abort();
    selectListener = null as unknown as AbortController;

    // console.log('abort nav listener');
  }
};

const saveToDatabase = async (newGlobalSettings: GlobalSettings) => {
  const data = {
    globalSettings: newGlobalSettings,
  };

  // setStorageData<GlobalSettings>(data);
  setStorageDataLocal<GlobalSettings>(data);
};

const mapGlobalSettings = (newItem: QueryItem) => {
  const newSelectedQueryList = [
    newItem,
    ...currentGlobalSettings.queryListState.selectedList.itemList,
  ];

  const newSelectedQuery = {
    ...currentGlobalSettings.queryListState.selectedList,
    itemList: newSelectedQueryList,
  };

  const newAllQueryLists = currentGlobalSettings.queryListState.queryList.map(
    (eachList) =>
      eachList.id === newSelectedQuery.id ? newSelectedQuery : eachList
  ) as any[];

  const newGlobalSettings: GlobalSettings = {
    ...currentGlobalSettings,
    queryListState: {
      ...currentGlobalSettings.queryListState,
      queryList: newAllQueryLists,
      selectedList: newSelectedQuery,
    },
  };

  return newGlobalSettings;
};
