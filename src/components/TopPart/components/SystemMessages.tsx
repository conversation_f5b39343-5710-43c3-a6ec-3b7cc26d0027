import { Button, Flex, SlideFade, Text } from "@chakra-ui/react";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { getStorageDataLocal } from "../../../chrome/chrome.utils";
import { PaddleSubscription } from "../../../definitions/global.definition";
import useStateManager from "../../../store/stateManager.hook";
import { alertAnimation } from "../../../styles/global.styles";

interface SystemMessagesType {
  coupon: string;
  systemMessage: string;
}

function isDateOlderThan5Days(dateString: string) {
  if (!dateString) return true;

  // Convert the input date string to a Date object
  const inputDate = new Date(dateString);

  // Get the current date
  const currentDate = new Date();

  // Calculate the difference in milliseconds between the current date and the input date
  const differenceInMs = Number(currentDate) - Number(inputDate);

  // Calculate the difference in days
  const differenceInDays = differenceInMs / (1000 * 60 * 60 * 24);

  // Check if the difference is greater than 5 days
  return differenceInDays > 5;
}

function SystemMessages() {
  const {
    freshRestart: { hasClickedRestart },
    subscription: { subscription },
    systemMessages: { systemMessageDate, onSetSystemMessages },
  } = useStateManager();

  const [messages, setMessages] = useState<SystemMessagesType>({
    coupon: "",
    systemMessage: "",
  });

  useEffect(() => {
    getStorageDataLocal<{
      subscription: PaddleSubscription;
    }>("subscription")
      ?.then(({ subscription }) =>
        hasClickedRestart && !subscription?.isActive
          ? fetch(`${process.env.REACT_APP_FIREBASE_DOMAIN}/getCurrentMessages`)
          : null
      )
      .then((res) => res?.json())
      .then((res: SystemMessagesType) => setMessages(res))
      .catch((err) => console.log(err, "system message error"));

    return () => {};
  }, [hasClickedRestart, subscription]);

  const isOlderThan5Days = isDateOlderThan5Days(systemMessageDate);

  if (!isOlderThan5Days) return null;

  return messages?.coupon || messages?.systemMessage ? (
    <SlideFade in={true}>
      <Flex
        as={motion.div}
        bg={"brand.purpleFull"}
        p="1"
        justify={"center"}
        rounded="md"
        align="center"
        flexDir={"column"}
        w="89%"
        mx="auto"
        mb="2"
        shadow={"lg"}
        animation={alertAnimation}
        _hover={{
          animationPlayState: "paused",
        }}
        userSelect="none"
        color="white"
        fontWeight={"500"}
        textAlign={"center"}
        fontSize={"11px"}
      >
        {messages?.coupon ? <Text>{messages.coupon}</Text> : null}
        {messages?.systemMessage ? <Text>{messages.systemMessage}</Text> : null}

        <Button
          mt="1"
          bg={"rgba(103, 186, 211, 0.53)"}
          _hover={{
            bg: "rgba(103, 186, 211, 0.30)",
          }}
          _active={{
            bg: "brand.menuBarBg",
          }}
          size="xs"
          fontSize={"11px"}
          onClick={onSetSystemMessages}
        >
          Understood!
        </Button>
      </Flex>
    </SlideFade>
  ) : null;
}

export default SystemMessages;
