import { setStorageDataLocal } from "../../../../chrome/chrome.utils";
import { WhatsNew } from "../../../../definitions/global.definition";

export const whatsNewCurrent: WhatsNew = {
  now: [
    "New (free) <a href='https://highlighty.app/blog/enhancing-accessibility-text-decoration-options' target='_blank' style='color: #6B46C1; text-decoration: underline;'>text decoration options</a> for visually impaired users (underline, overline, etc.)",
    "New (free) <a href='https://highlighty.app/blog/introducing-scrollbar-markers' target='_blank' style='color: #6B46C1; text-decoration: underline;'>scrollbar markers</a> feature with clickable positions to navigate to highlighted text",
    "Highlight effect animation when clicking scrollbar markers for better visibility",
    "Improved sign-up flow",
  ],

  future: [],
  version: "2.3.5",
  //   isViewed: false,
};

export const saveWhatsNew = async () => {
  try {
    const data = {
      whatsNew: whatsNewCurrent,
    };

    // console.log("Saving WhatsNew data:", data);
    await setStorageDataLocal(data);
    // console.log("WhatsNew data saved successfully");
  } catch (e) {
    console.error("Error saving WhatsNew data:", e);
  }
};
