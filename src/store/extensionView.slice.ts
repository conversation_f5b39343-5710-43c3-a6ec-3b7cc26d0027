import type { PayloadAction } from "@reduxjs/toolkit";
import { createSlice } from "@reduxjs/toolkit";
import { paidFeatures } from "../components/shared/utils/constants.consts";
import { ExtensionViewState, MenuView } from "../definitions/global.definition";
import { getCurrentDate } from "../components/MidPart/components/AccountTab/cachedAccount.utils";

export const extensionViewInit: ExtensionViewState = {
  isMenuOpen: false,
  isMinCharLimit: true,
  isInputFocus: true,
  isDarkTheme: false,
  hasClickedRestart: false,
  systemMessageDate: "",
  menuView: 0,
  isAccessibilitySettings: false,
  ...paidFeatures.extensionView,
};

export const extensionViewSlice = createSlice({
  name: "extensionViewState",
  initialState: extensionViewInit,
  reducers: {
    setIsToggleMenuOpen: (state) => {
      state.isMenuOpen = !state.isMenuOpen;
    },

    setIsMenuOpen: (state, action: PayloadAction<boolean>) => {
      state.isMenuOpen = action.payload;
    },
    setHasClickedRestart: (state) => {
      state.hasClickedRestart = true;
    },
    setSystemMessagesDate: (state) => {
      state.systemMessageDate = getCurrentDate();
    },

    setMenuView: (state, action: PayloadAction<MenuView>) => {
      state.menuView = action.payload;
    },

    setIsToggleColorPicker: (state) => {
      state.isColorPickerView = !state.isColorPickerView;
    },

    setIsColorPicker: (state, action: PayloadAction<boolean>) => {
      state.isColorPickerView = action.payload;
    },
    setIstoggleColorPalette: (state) => {
      state.colorPalette.isOn = !state.colorPalette.isOn;
    },
    setIstoggleDynamicColorPalette: (state) => {
      state.colorPalette.isDynamic = !state.colorPalette.isDynamic;
    },
    setIsDynamicPalette: (state, action: PayloadAction<boolean>) => {
      state.colorPalette.isDynamic = action.payload;
    },

    setIsMinCharLimit: (state, { payload }: PayloadAction<boolean>) => {
      state.isMinCharLimit = payload;
    },

    setIsInputFocus: (state, { payload }: PayloadAction<boolean>) => {
      state.isInputFocus = payload;
    },

    setIsDarkTheme: (state) => {
      state.isDarkTheme = !state.isDarkTheme;
    },

    setIsToggleAccessibilitySettings: (state) => {
      state.isAccessibilitySettings = !state.isAccessibilitySettings;
    },

    setIsAccessibilitySettings: (state, { payload }: PayloadAction<boolean>) => {
      state.isAccessibilitySettings = payload;
    },

    setExtensionViewSettings: (
      state,
      action: PayloadAction<ExtensionViewState>
    ) => (state = action.payload),
  },
});

export const {
  setIsToggleMenuOpen,
  setIsMenuOpen,
  setIsToggleColorPicker,
  setIsColorPicker,
  setExtensionViewSettings,
  setMenuView,
  setIsMinCharLimit,
  setIsInputFocus,
  setIsDarkTheme,
  setHasClickedRestart,
  setIstoggleColorPalette,
  setIsDynamicPalette,
  setIstoggleDynamicColorPalette,
  setSystemMessagesDate,
  setIsToggleAccessibilitySettings,
  setIsAccessibilitySettings,
} = extensionViewSlice.actions;

export default extensionViewSlice.reducer;
