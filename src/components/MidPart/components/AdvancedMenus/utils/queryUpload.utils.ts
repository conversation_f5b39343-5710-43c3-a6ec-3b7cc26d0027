import { QueryItem } from '../../../../../definitions/global.definition';
import { ToastTypesPopup } from '../../../../shared/components/Toast/CustomToast.hook';

export function removeDuplicatesQueryList(
  uploadedDoc: QueryItem[],
  globalQueryList: QueryItem[]
) {
  const removedDuplicatesFromExistingList: QueryItem[] = uploadedDoc?.reduce(
    (acc, curr: QueryItem) => {
      return globalQueryList.find(
        (oldListItem) => oldListItem.name === curr.name
      )
        ? acc
        : (acc = [...acc, curr]);
    },
    [] as QueryItem[]
  );

  return removedDuplicatesFromExistingList;
}

export function limitFreeUpload(
  processedList: QueryItem[],
  currentQueryListLength: number
) {
  const limitedUploadLength =
    2 + 1 + 1 + 1 === currentQueryListLength
      ? 0
      : 2 + 1 + 1 + 1 - currentQueryListLength;

  const limitedUploadList = processedList.slice(0, limitedUploadLength);

  return limitedUploadList;
}

export function postLimitUploader(
  limitedUploadList: QueryItem[],
  onUploadItemQueryList: (list: QueryItem[]) => void,
  customToast: (message: string, type: ToastTypesPopup) => void
) {
  if (limitedUploadList.length === 0)
    return customToast('Reached limit of 5 queries.', 'error');

  onUploadItemQueryList(limitedUploadList);
  customToast(`Uploaded ${limitedUploadList.length} queries.`, 'success');
}
