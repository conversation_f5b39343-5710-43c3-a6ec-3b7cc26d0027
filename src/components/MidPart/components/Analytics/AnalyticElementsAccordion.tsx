import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Flex,
  Text,
} from '@chakra-ui/react';
import { AnalyticsData } from '../../../../chrome/features/content.analytics';
import { hslFormatter } from '../../../shared/utils/color.utils';

interface Props {
  analyticsData: AnalyticsData[];
}

function AnalyticElementsAccordion({ analyticsData }: Props) {
  function getPercentage(querySum: number, totalNumber: number) {
    return querySum ? (querySum / totalNumber) * 100 : 0;
  }

  return (
    <Accordion
      maxH="300px"
      overflowX={'hidden'}
      overflowY={'auto'}
      p="0"
      m="0"
      allowMultiple
      reduceMotion
    >
      {analyticsData?.map(({ url, visitTime, data, totalSum }, i) => (
        <AccordionItem rounded="md" key={i}>
          {({ isExpanded }) => (
            <>
              <AccordionButton
                rounded="md"
                shadow={isExpanded ? 'sm' : ''}
                bg={isExpanded ? 'brand.purple' : 'auto'}
                _hover={{ bg: 'brand.purpleHover' }}
                fontSize={11}
                p="0"
                pl="1"
              >
                <Flex
                  flexDir={'column'}
                  p="0"
                  as="span"
                  flex="1"
                  textAlign="left"
                >
                  <Text>{visitTime}</Text>

                  {/* <Tooltip label={url} {...tooltipStyle} wordBreak={'keep-all'}> */}
                  <Text
                    maxH="32px"
                    overflow={'hidden'}
                    fontStyle={'i'}
                    wordBreak={'break-all'}
                  >
                    {url}
                  </Text>
                  {/* </Tooltip> */}
                </Flex>
                <AccordionIcon />
              </AccordionButton>

              <AccordionPanel fontSize={11} p="0">
                {data.map((query, i) => (
                  // <Tooltip label={query?.queryItem?.name} {...tooltipStyle}>
                  <Flex
                    key={i}
                    flexDir="column"
                    rounded="sm"
                    overflow={'hidden'}
                    my="1"
                    w="100%"
                    position={'relative'}
                  >
                    <Flex
                      position="absolute"
                      w="100%"
                      h="100%"
                      bg={hslFormatter(query?.queryItem?.colors?.hsl)}
                      opacity="0.07"
                    ></Flex>
                    <Flex zIndex={2}>
                      <Text fontWeight="700" cursor="default" pl="2px">
                        Query:
                      </Text>
                      <Text cursor="default" pl="2px">
                        {query?.queryItem?.name}
                      </Text>
                    </Flex>

                    <Flex pl="2px" zIndex={2}>
                      <Text fontWeight="700" cursor="default">
                        Hits:
                      </Text>
                      <Text
                        // color={
                        //   isBrightColor(query?.queryItem?.colors?.hsl)
                        //     ? 'brand.textColor'
                        //     : 'white'
                        // }
                        cursor="default"
                      >
                        {query?.collectedData?.number ?? 0}
                      </Text>
                    </Flex>

                    <Flex position="relative" shadow="sm" h="20px" w="100%">
                      <Flex
                        top="0"
                        left="0"
                        h="100%"
                        bg={hslFormatter(query?.queryItem?.colors?.hsl)}
                        position="absolute"
                        w={`${getPercentage(
                          query?.collectedData?.number,
                          totalSum
                        )}%`}
                        rounded="sm"
                      ></Flex>
                      <Flex
                        rounded="sm"
                        top="0"
                        left="0"
                        h="100%"
                        bg={hslFormatter(query?.queryItem?.colors?.hsl)}
                        opacity="0.1"
                        position="absolute"
                        w={'100%'}
                      ></Flex>
                    </Flex>
                  </Flex>
                  // </Tooltip>
                ))}
              </AccordionPanel>
            </>
          )}
        </AccordionItem>
      ))}
    </Accordion>
  );
}

export default AnalyticElementsAccordion;
