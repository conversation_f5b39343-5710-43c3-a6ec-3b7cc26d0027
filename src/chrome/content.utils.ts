import { doUnmark } from "./features/content.highlight";

import { GlobalSettings } from "../definitions/global.definition";
import { ChromeMessage, Sender } from "../types";

import { MessageResponse } from "./content.definitions";
import { collectAnalyticsData } from "./features/content.analytics";
import {
  isBlackListedSite,
  isWhiteListedSite,
} from "./features/content.blackWhitelist";
import { disableColorsOnKeyStroke } from "./features/content.disableColors";
import { hasGlobalSettingsChanged } from "./features/content.globalSettingsState";
import { navigation } from "./features/content.navigation";
import { observerLogic } from "./features/content.observer";
import { initSearchBySelect } from "./features/content.searchBySelect";
import { initSearchOnHotkey } from "./features/content.searchOnHotkey";
import { toggleScrollbarHighlighter } from "./features/content.scrollbarHighlighter";
import { showFoundCountOnIconCleanup } from "./features/content.sumBadge";
import { switchOffHotkey } from "./features/content.switchOffHotkey";
import { BROWSER_CALLER } from "./shared";

export const validateSender = (
  message: ChromeMessage,
  sender: chrome.runtime.MessageSender
) => {
  return (
    hasGlobalSettingsChanged(message?.message?.globalSettings) &&
    sender.id === BROWSER_CALLER.runtime.id &&
    message.from === Sender.React
  );
};

export const receiveMessageFromPopup = async (
  message: ChromeMessage,
  sender: chrome.runtime.MessageSender,
  response: MessageResponse
) => {
  const isValidated = validateSender(message, sender);

  if (!isValidated) return;

  const isMessagingTrigger = true;

  const globalSettings = message.message.globalSettings;

  // Toggle scrollbar highlighter based on settings
  if (globalSettings && globalSettings.scrollbarHighlighter !== undefined) {
    toggleScrollbarHighlighter(globalSettings.scrollbarHighlighter.isOn);
  }

  invokeBehaviour(globalSettings, isMessagingTrigger);

  try {
    response({ success: true });
    // console.log("✅ Response sent!");
  } catch (error) {
    console.error("❌ Error in content.js + popup messaging:", error);
    response({ success: false, error: (error as any).message });
  }
};

// Exported async function with debounce logic
export const contentFileLogic = async (
  currentGlobalSettings: GlobalSettings,
  isObserver?: boolean,
  isMessagingTrigger?: boolean
) => {
  initSearchOnHotkey(isObserver);

  await collectAnalyticsData(
    currentGlobalSettings,
    isObserver,
    isMessagingTrigger
  );

  disableColorsOnKeyStroke(currentGlobalSettings, isObserver);

  navigation(currentGlobalSettings, isObserver, isMessagingTrigger);

  initSearchBySelect();
};

export const invokeBehaviour = async (
  currentGlobalSettings: GlobalSettings,
  isMessagingTrigger?: boolean
) => {
  const isNotEligible =
    !currentGlobalSettings ||
    isBlackListedSite(currentGlobalSettings) ||
    !isWhiteListedSite(currentGlobalSettings);

  if (isNotEligible) return;

  // has to work even when the extension is turned off
  switchOffHotkey();

  if (!currentGlobalSettings.isExtensionOn) {
    // cleanup after turning off

    switchOffCleanup();
    return;
  }

  await contentFileLogic(currentGlobalSettings, false, isMessagingTrigger);

  if (!isMessagingTrigger) observerLogic();
};

export const switchOffCleanup = () => {
  doUnmark();
  showFoundCountOnIconCleanup(); // removes the badge from the extension icon
  toggleScrollbarHighlighter(false); // disable scrollbar highlighter
};
