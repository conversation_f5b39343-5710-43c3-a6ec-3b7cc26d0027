import { QueryItem } from "../../../definitions/global.definition";
import { adaptiveFontColor, hslFormatter } from "../../../components/shared/utils/color.utils";
import { dataHility, hilityTag } from "../../content.consts";

/**
 * Convert decoration thickness setting to CSS value
 */
export const getThicknessValue = (thickness?: string): string => {
  return thickness === "thin"
    ? "3px"
    : thickness === "medium"
    ? "4px"
    : thickness === "thick"
    ? "5px"
    : thickness === "very-thick"
    ? "6px"
    : thickness === "extra-thick"
    ? "7px"
    : "auto";
};

/**
 * Helper function to generate CSS for different decoration types
 */
export const getDecorationCSS = (queryItem: QueryItem): string => {
  const color = hslFormatter(queryItem.colors.hsl);
  // Ensure we're using the decoration type from the queryItem, defaulting to 'background'
  const type = queryItem.decorationType || "background";
  const thickness = queryItem.decorationThickness || "medium";

  // Get thickness value using the shared function
  const thicknessValue = getThicknessValue(thickness);

  // Log the decoration type and thickness for debugging
  // console.log(`Applying decoration: ${type}, thickness: ${thickness}, color: ${color}`);

  switch (type) {
    case "background":
      return `
        background: ${color};
        color: ${adaptiveFontColor(queryItem.colors.hsl, true)};
        height: 100%;
        border-radius: 4px;
        box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.65);
      `;
    case "underline":
      return `
        text-decoration-line: underline;
        text-decoration-color: ${color};
        text-decoration-thickness: ${thicknessValue};
        background: transparent;
        color: inherit;
      `;
    case "overline":
      return `
        text-decoration-line: overline;
        text-decoration-color: ${color};
        text-decoration-thickness: ${thicknessValue};
        background: transparent;
        color: inherit;
      `;
    case "line-through":
      return `
        text-decoration-line: line-through;
        text-decoration-color: ${color};
        text-decoration-thickness: ${thicknessValue};
        background: transparent;
        color: inherit;
      `;
    case "dotted-underline":
      return `
        text-decoration-line: underline;
        text-decoration-style: dotted;
        text-decoration-color: ${color};
        text-decoration-thickness: ${thicknessValue};
        background: transparent;
        color: inherit;
      `;
    case "wavy-underline":
      return `
        text-decoration-line: underline;
        text-decoration-style: wavy;
        text-decoration-color: ${color};
        text-decoration-thickness: ${thicknessValue};
        background: transparent;
        color: inherit;
      `;
    case "double-underline":
      return `
        text-decoration-line: underline;
        text-decoration-style: double;
        text-decoration-color: ${color};
        text-decoration-thickness: ${thicknessValue};
        background: transparent;
        color: inherit;
      `;
    default:
      return `
        background: ${color};
        color: ${adaptiveFontColor(queryItem.colors.hsl, true)};
        height: 100%;
        border-radius: 4px;
        box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.65);
      `;
  }
};

/**
 * Apply decoration styles to a mark element based on the query item's decoration settings
 */
export const applyDecorationToMark = (mark: HTMLElement, item: QueryItem) => {
  // Set the data attribute for the highlight ID
  mark.setAttribute(dataHility, item.id);

  // Set data attribute for decoration type
  const decorationType = item.decorationType || "background";
  mark.setAttribute("data-decoration-type", decorationType);

  // Get thickness value based on the decoration thickness setting
  const thicknessValue = getThicknessValue(item.decorationThickness);

  // Set CSS variable for thickness
  mark.style.setProperty("--decoration-thickness", thicknessValue);

  // Apply direct styles for text decorations
  if (decorationType && decorationType !== "background") {
    const color = hslFormatter(item.colors.hsl);

    switch (decorationType) {
      case "underline":
        mark.style.textDecorationLine = "underline";
        mark.style.textDecorationColor = color;
        mark.style.textDecorationThickness = thicknessValue;
        mark.style.background = "transparent";
        break;
      case "overline":
        mark.style.textDecorationLine = "overline";
        mark.style.textDecorationColor = color;
        mark.style.textDecorationThickness = thicknessValue;
        mark.style.background = "transparent";
        break;
      case "line-through":
        mark.style.textDecorationLine = "line-through";
        mark.style.textDecorationColor = color;
        mark.style.textDecorationThickness = thicknessValue;
        mark.style.background = "transparent";
        break;
      case "dotted-underline":
        mark.style.textDecorationLine = "underline";
        mark.style.textDecorationStyle = "dotted";
        mark.style.textDecorationColor = color;
        mark.style.textDecorationThickness = thicknessValue;
        mark.style.background = "transparent";
        break;
      case "wavy-underline":
        mark.style.textDecorationLine = "underline";
        mark.style.textDecorationStyle = "wavy";
        mark.style.textDecorationColor = color;
        mark.style.textDecorationThickness = thicknessValue;
        mark.style.background = "transparent";
        break;
      case "double-underline":
        mark.style.textDecorationLine = "underline";
        mark.style.textDecorationStyle = "double";
        mark.style.textDecorationColor = color;
        mark.style.textDecorationThickness = thicknessValue;
        mark.style.background = "transparent";
        break;
    }
  }
};

/**
 * Add browser compatibility fallbacks for text decoration styles
 */
export const addTextDecorationFallbacks = () => {
  // Check if we've already added the fallbacks
  if (document.getElementById("highlighty-text-decoration-fallbacks")) {
    return;
  }

  // console.log("Adding text decoration fallbacks");

  // Get all query items to create specific color rules for each
  const queryItems = document.querySelectorAll(`[${dataHility}]`);
  let colorRules = "";

  // Create specific color rules for each query item
  queryItems.forEach((item) => {
    const id = item.getAttribute(dataHility);
    const decorationType = item.getAttribute("data-decoration-type");

    if (decorationType && decorationType !== "background") {
      // Get the color from the inline style
      const style = window.getComputedStyle(item);
      const color = style.getPropertyValue("text-decoration-color");

      if (color) {
        colorRules += `
        /* Color rule for ${id} */
        ${hilityTag}[${dataHility}="${id}"] {
          text-decoration-color: ${color} !important;
          border-bottom-color: ${color} !important;
          border-top-color: ${color} !important;
        }

        /* Ensure color is applied with higher specificity */
        ${hilityTag}[${dataHility}="${id}"][data-decoration-type] {
          text-decoration-color: ${color} !important;
          border-bottom-color: ${color} !important;
          border-top-color: ${color} !important;
        }
        `;
      }
    }
  });

  const fallbackStyles = document.createElement("style");
  fallbackStyles.id = "highlighty-text-decoration-fallbacks";
  fallbackStyles.textContent = `
    /* Basic fallbacks for all decoration types - using individual properties instead of shorthand */
    ${hilityTag}[data-decoration-type="underline"] {
      text-decoration-line: underline !important;
      text-decoration-thickness: var(--decoration-thickness, 2px) !important;
      background: transparent !important;
    }

    ${hilityTag}[data-decoration-type="overline"] {
      text-decoration-line: overline !important;
      text-decoration-thickness: var(--decoration-thickness, 2px) !important;
      background: transparent !important;
    }

    ${hilityTag}[data-decoration-type="line-through"] {
      text-decoration-line: line-through !important;
      text-decoration-thickness: var(--decoration-thickness, 2px) !important;
      background: transparent !important;
    }

    /* Thickness-specific styles */
    ${hilityTag}[data-decoration-type][style*="--decoration-thickness: 1px"] {
      text-decoration-thickness: 1px !important;
    }

    ${hilityTag}[data-decoration-type][style*="--decoration-thickness: 2px"] {
      text-decoration-thickness: 2px !important;
    }

    ${hilityTag}[data-decoration-type][style*="--decoration-thickness: 3px"] {
      text-decoration-thickness: 3px !important;
    }

    ${hilityTag}[data-decoration-type][style*="--decoration-thickness: 4px"] {
      text-decoration-thickness: 4px !important;
    }

    ${hilityTag}[data-decoration-type][style*="--decoration-thickness: 5px"] {
      text-decoration-thickness: 5px !important;
    }

    /* Fallbacks for browsers that don't support text-decoration-style */
    @supports not (text-decoration-style: dotted) {
      ${hilityTag}[data-decoration-type="dotted-underline"] {
        border-bottom-style: dotted !important;
        border-bottom-width: var(--decoration-thickness, 2px) !important;
        text-decoration: none !important;
      }

      ${hilityTag}[data-decoration-type="wavy-underline"] {
        border-bottom-style: solid !important;
        border-bottom-width: var(--decoration-thickness, 2px) !important;
        text-decoration: none !important;
      }

      ${hilityTag}[data-decoration-type="double-underline"] {
        border-bottom-style: double !important;
        border-bottom-width: var(--decoration-thickness, 3px) !important;
        text-decoration: none !important;
      }
    }

    /* Styles for browsers that do support text-decoration-style */
    @supports (text-decoration-style: dotted) {
      ${hilityTag}[data-decoration-type="dotted-underline"] {
        text-decoration-line: underline !important;
        text-decoration-style: dotted !important;
        text-decoration-thickness: var(--decoration-thickness, 2px) !important;
        background: transparent !important;
      }

      ${hilityTag}[data-decoration-type="wavy-underline"] {
        text-decoration-line: underline !important;
        text-decoration-style: wavy !important;
        text-decoration-thickness: var(--decoration-thickness, 2px) !important;
        background: transparent !important;
      }

      ${hilityTag}[data-decoration-type="double-underline"] {
        text-decoration-line: underline !important;
        text-decoration-style: double !important;
        text-decoration-thickness: var(--decoration-thickness, 3px) !important;
        background: transparent !important;
      }
    }

    /* Color-specific rules for each query item */
    ${colorRules}
  `;

  document.head.appendChild(fallbackStyles);
};