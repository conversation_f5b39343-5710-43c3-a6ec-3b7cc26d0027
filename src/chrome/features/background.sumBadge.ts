export const addBadgeText = (foundQueries?: number, tabId?: number) => {
  // ADDING TEXT TO BADGE - FOUND NUMBERS

  if (
    !chrome?.action ||
    !chrome?.action?.setBadgeText ||
    !chrome?.action?.setBadgeBackgroundColor
  )
    return;

  chrome.action.setBadgeBackgroundColor({ color: 'rgb(118, 244, 233)' });

  // console.log(foundQueries, 'setBadge');

  chrome.action.setBadgeText({
    text: foundQueries?.toString() || '',
    tabId: tabId,
  });
};
