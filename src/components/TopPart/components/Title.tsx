import { Flex, Heading, Text } from '@chakra-ui/react';
import { clearStorageDataLocal } from '../../../chrome/chrome.utils';
import { brandTitleStyle } from '../../../styles/global.styles';

function Title() {
  return (
    <Flex
      height="auto"
      width="100%"
      justify={'center'}
      pt="5px"
      pb="2px"
      w="100%"
      pos="relative"
      onClick={() =>
        process.env.REACT_APP_ENV ? clearStorageDataLocal() : undefined
      }
    >
      <Heading
        {...brandTitleStyle}
        display="inline-block"
        borderRadius="5px 0 0 5px"
        pl="6px"
        padding-bottom="2px"
        backgroundColor="brand.purple"
      >
        HIGH
      </Heading>

      <Heading
        {...brandTitleStyle}
        borderRadius=" 0 5px 5px 0"
        pl="3.5px"
        pb="2px"
        pr="7px"
        backgroundColor="brand.blue"
      >
        LIGHTY
      </Heading>

      <Text
        right="30px"
        top="28px"
        fontSize={'6px'}
        fontWeight="bold"
        pos="absolute"
        rounded="2xl"
        bg="brand.purple"
        p="1.1px"
      >
        v2
      </Text>
    </Flex>
  );
}

export default Title;
