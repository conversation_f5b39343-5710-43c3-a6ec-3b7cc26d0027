import useStateManager from "../../../../../store/stateManager.hook";
import MenuDropdownItem from "../../MenuDropdownItem";

function QueryClickInvertedFeature() {
  const { globalSettings } = useStateManager();

  return (
    <MenuDropdownItem
      onYes={() => globalSettings.onSetIsQueryClickInverted(true)}
      onNo={() => globalSettings.onSetIsQueryClickInverted(false)}
      isYes={globalSettings.isQueryClickInverted?.isOn}
      label="Invert query box click action"
      labelTooltip="Left click will trigger query editing, right click will delete the query"
    />
  );
}

export default QueryClickInvertedFeature;
