import { User } from "firebase/auth";
import { ColorResult } from "react-color";

export interface QueryItem {
  name: string;
  colors: ColorResult;
  id: string;
  isInactive: boolean;
  isRegexp: boolean;
  // New decoration properties for accessibility
  decorationType?:
    | "background"
    | "underline"
    | "overline"
    | "line-through"
    | "dotted-underline"
    | "wavy-underline"
    | "double-underline";
  decorationThickness?:
    | "thin"
    | "medium"
    | "thick"
    | "very-thick"
    | "extra-thick"
    | "auto";
}
export interface ColorPalette {
  isOn: boolean;
  isDynamic: boolean;
}

export interface ExtensionViewState {
  systemMessageDate: string;
  isMenuOpen: boolean;
  isColorPickerView: boolean;
  isMinCharLimit: boolean;
  isInputFocus: boolean;
  isDarkTheme: boolean;
  hasClickedRestart: boolean;
  menuView: MenuView;
  colorPalette: ColorPalette;
  isAccessibilitySettings?: boolean;
}

export type MenuView = 0 | 1 | 2 | 3;

export interface QueryInputProps {
  setNewItem: React.Dispatch<React.SetStateAction<QueryItem>>;
  newItem: QueryItem;
}

export interface WebsiteLists {
  websiteList: string;
  isOn: boolean;
}

export interface QueryListItem {
  name: string;
  value: string;
  id: string;
  itemList: QueryItem[];
}

export interface QueryListState {
  selectedList: QueryListItem;
  queryList: QueryListItem[];
}

export interface NavigationBar {
  isOn: boolean;
  hasWordFocus: boolean;
  focusedWord: any;
  assignedButtons: { upButtonKey: string; downButtonKey: string };
}

export interface GlobalSettings {
  //
  isExtensionOn: boolean;
  queryListState: QueryListState;
  whitelistSites: WebsiteLists;
  blacklistSites: WebsiteLists;
  navigationBar: NavigationBar;
  //
  analytics: { isOn: boolean };
  switchOffHotkey: { isOn: boolean };
  foundSizeIncrease: { isOn: boolean };
  badgeFoundSum: { isOn: boolean };
  queryNotFound: { isOn: boolean };
  queryFound: { isOn: boolean };
  caseSens: { isOn: boolean };
  diacriticSens: { isOn: boolean };
  splitSearch: { isOn: boolean };
  completeWordSearch: { isOn: boolean };
  iframeSearch: { isOn: boolean };
  searchBySelect: { isOn: boolean; isBright: boolean };
  temporaryDeselect: { isOn: boolean };
  isCompactVIew: { isOn: boolean };
  isQueryClickInverted: { isOn: boolean };
  isQueryClickDisabled: { isOn: boolean };
  isSeachOnHotkey: { isOn: boolean };
  scrollbarHighlighter?: { isOn: boolean };
}

export interface OldGlobalSettings {
  isExtensionOn: boolean;
  isFoundSizeIncrease: boolean;
  isBadgeFoundSum: boolean;
  hasQueryNotFound: boolean;
  hasQueryFound: boolean;
  navigationBar: NavigationBar;
  isCaseSens: boolean;
  isDiacriticSens: boolean;
  isSplitSearch: boolean;
  isCompleteWordSearch: boolean;
  isIframeSearch: boolean;
  isSearchBySelect: boolean;
  queryListState: QueryListState;
  whitelistSites: WebsiteLists;
  blacklistSites: WebsiteLists;
  isTemporaryDeselect: boolean;
}

export interface WhatsNew {
  now: string[];
  future: string[];
  isViewed?: boolean;
  version: string;
}

export type UserAccount = User | null;
export type UserSubscription = PaddleSubscription | null;

export interface AccountSubscription {
  account: UserAccount;
  subscription: UserSubscription;
  isLoading?: boolean;
}

export type SubscriptionStatus =
  | "trialing"
  | "active"
  | "past_due"
  | "canceled"
  | "unpaid"
  | "deleted";

export interface PaddleSubscription {
  currentPeriodStart: string;
  currentPeriodEnd: string;
  isActive: boolean;
  timeStamp: string;
  cancelUrl: string;
  updateUrl: string;
  subscriptionStatus: SubscriptionStatus;
  isCanceled: boolean;
  isPaddle: boolean;
}
