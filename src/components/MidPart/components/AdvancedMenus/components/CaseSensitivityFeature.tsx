import useStateManager from "../../../../../store/stateManager.hook";
import MenuDropdownItem from "../../MenuDropdownItem";

function CaseSensitivityFeature() {
  const { globalSettings } = useStateManager();

  return (
    <MenuDropdownItem
      onYes={() => globalSettings.onIsCaseSens(true)}
      onNo={() => globalSettings.onIsCaseSens(false)}
      isYes={globalSettings.caseSens.isOn}
      label="Case sensitivity"
      labelTooltip="Case sensitivity is used to determine if the search should be case sensitive or not."
    />
  );
}

export default CaseSensitivityFeature;
