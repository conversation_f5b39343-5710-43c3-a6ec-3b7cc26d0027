import { Flex, Text, Tooltip } from "@chakra-ui/react";
import { useState } from "react";
import { tooltipStyle } from "../../../../styles/global.styles";

function DeleteAccount() {
  // const [isDeleteLoading, setIsDeleteLoading] = useState(false);
  // const [isDeletePrompt, setIsDeletePrompt] = useState(false);
  const [isDeleteConfirmation, setIsDeleteConfirmation] = useState(false);

  // const [loginData, setLoginData] = useState({
  //   email: "",
  //   password: "",
  // });

  // function onChangeInput(e: ChangeEvent<HTMLInputElement>) {
  //   switch (e.target.name) {
  //     case "email":
  //       setLoginData((state) => ({ ...state, email: e.target.value }));
  //       break;
  //     case "password":
  //       setLoginData((state) => ({ ...state, password: e.target.value }));
  //       break;
  //   }
  // }

  // const customToast = useCustomToast();

  // async function onLoginWithEmail(e: any) {
  //   e.preventDefault();

  //   await singInWithEmail(
  //     loginData.email,
  //     loginData.password,
  //     customToast,
  //     () => setIsDeletePrompt(false)
  //   );

  //   deleteAccount(setIsDeleteLoading, setIsDeletePrompt, customToast);
  // }

  return (
    <Flex
      ml="2"
      align="center"
      w="100%"
      justify="space-between"
      fontSize={"11px"}
      rounded="md"
    >
      <Tooltip
        {...tooltipStyle}
        maxW="125px"
        placement="right-end"
        bg="red.500"
        label={
          isDeleteConfirmation
            ? ""
            : "All your data will permanently be removed from our servers."
        }
      >
        <Flex align={"center"}>
          {/* {isDeleteLoading ? <Spinner w="10px" h="10px" /> : null} */}
          {isDeleteConfirmation ? (
            <Flex
              fontSize={"9px"}
              fontStyle={"italic"}
              w="100%"
              justify="space-between"
            >
              <Text
                // onClick={() =>
                //   deleteAccount(
                //     setIsDeleteLoading,
                //     setIsDeletePrompt,
                //     customToast
                //   )
                // }
                as={"a"}
                href="https://highlighty.app/support-redirect"
                rel="noreferrer"
                target="_blank"
                cursor="pointer"
                color="red.500"
              >
                I'm sure I want to delete
              </Text>
              <Text
                onClick={() => setIsDeleteConfirmation(false)}
                cursor="pointer"
                fontWeight="600"
                ml="5"
              >
                Nah, don't delete.
              </Text>
            </Flex>
          ) : (
            <Text
              onClick={() => setIsDeleteConfirmation(true)}
              cursor="pointer"
              transition="all 200ms"
              color="red.500"
              _hover={{ color: "red.500" }}
              fontStyle={"italic"}
              fontSize={"9px"}
            >
              delete my data
            </Text>
          )}
        </Flex>
      </Tooltip>

      {/* {isDeletePrompt ? (
        <Flex
          left="30px"
          top="30%"
          w="210px"
          h="180px"
          pos="absolute"
          justify={"center"}
          align="center"
          rounded="md"
          border="2px solid"
          borderColor="brand.purple"
          bg="whiteAlpha.900"
          shadow="md"
          flexDir={"column"}
          zIndex="9999"
          p="1"
        >
          <Modal
            blockScrollOnMount={false}
            isOpen={isDeletePrompt}
            onClose={() => undefined}
          >
            <ModalOverlay zIndex={"0"} />
          </Modal>
          <Heading mt="0" textTransform={"uppercase"} fontSize={"13px"}>
            Please sign in to delete
          </Heading>
          <Flex justify={"space-evenly"} h="85%" as="form" flexDir={"column"}>
            <Input
              h="25px"
              {...blueInputStyle}
              onChange={onChangeInput}
              w="100%"
              value={loginData.email}
              type={"email"}
              placeholder="email"
              mt="2"
              mb="1"
              name="email"
              isRequired
            />

            <Input
              h="25px"
              {...blueInputStyle}
              onChange={onChangeInput}
              w="100%"
              type={"password"}
              value={loginData.password}
              placeholder="password"
              mb="1"
              name="password"
              isRequired
            />

            <Button
              onClick={onLoginWithEmail}
              type="submit"
              colorScheme={"whatsapp"}
              size="xs"
            >
              Confirm
            </Button>
            <Button
              onClick={() => setIsDeletePrompt(false)}
              colorScheme={"red"}
              size="xs"
            >
              Cancel
            </Button>
          </Flex>
        </Flex>
      ) : null} */}
    </Flex>
  );
}

export default DeleteAccount;
