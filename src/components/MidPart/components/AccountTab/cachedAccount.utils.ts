import { User } from "firebase/auth";
import {
  getStorageDataLocal,
  setStorageDataLocal,
} from "../../../../chrome/chrome.utils";
import {
  PaddleSubscription,
  UserAccount,
  UserSubscription,
} from "../../../../definitions/global.definition";

export interface AuthStateCache {
  user: User;
  paddle: PaddleSubscription;
  date: string;
}

export function getCurrentDate(): string {
  const today = new Date();
  const year = today.getFullYear().toString();
  const month = (today.getMonth() + 1).toString().padStart(2, "0");
  const day = today.getDate().toString().padStart(2, "0");
  return `${year}-${month}-${day}`;
}

export async function getAuthStateCache(
  onSetAccount: (account: UserAccount | null) => void,
  onSetSubscription: (subscription: UserSubscription | null) => void
) {
  const getCachedAuth = await getStorageDataLocal<{
    cachedAuth: AuthStateCache;
  }>("cachedAuth");

  const cachedAuth = getCachedAuth?.cachedAuth;

  return cachedAuth;

}
export async function setAuthStateCache(
  user: User | null,
  paddle: UserSubscription | null
) {
  const authState = {
    cachedAuth: {
      user,
      paddle,
      date: !user && !paddle ? null : getCurrentDate(),
    } as AuthStateCache,
  };

  setStorageDataLocal(authState);
}
