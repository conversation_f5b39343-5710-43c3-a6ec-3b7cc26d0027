import {
  Button,
  Fade,
  Flex,
  Heading,
  Icon,
  Modal,
  ModalOverlay,
  Text,
  useOutsideClick
} from '@chakra-ui/react';
import { useRef } from 'react';
import { AiOutlineClose } from 'react-icons/ai';
import { QueryListItem } from '../../../../definitions/global.definition';

interface Props {
  isOpen: boolean;
  onIsOpen: () => void;
  onDeleteList: () => void;
  selectedList: QueryListItem;
}

function PopupConfirmation({
  isOpen,
  selectedList,
  onIsOpen,
  onDeleteList,
}: Props) {
  const outsideRef = useRef<any>();

  useOutsideClick({
    ref: outsideRef,
    handler: () => onIsOpen(),
  });

  return (
    <>
      <Fade in={isOpen} style={{ transition: 'all 100ms', zIndex: 2 }}>
        <Flex
          ref={outsideRef}
          zIndex={'2'}
          position={'absolute'}
          w="80%"
          top="20%"
          mx="auto"
          bg="white"
          shadow="lg"
          rounded="lg"
          flexDir={'column'}
          p="2"
        >
          <Flex align="center" justify="space-between">
            <Heading fontWeight={600} fontSize="14px">
              Confirm List Removal
            </Heading>
            <Icon
              fontSize="15px"
              transition="all 100ms"
              _hover={{ color: 'brand.blueFull' }}
              as={AiOutlineClose}
              cursor={'pointer'}
              onClick={onIsOpen}
            />
          </Flex>
          <Text my="4" fontSize={'11px'}>
            Could you please confirm the removal of
            <i>
              <b> {selectedList.name} </b>
            </i>
            with <b>{selectedList?.itemList?.length}</b> items in it?
          </Text>

          <Flex justify={'flex-end'} w="100%">
            <Button
              colorScheme={'red'}
              fontSize={'11px'}
              onClick={onDeleteList}
              mr="2"
              size="xs"
            >
              Delete
            </Button>
            <Button fontSize={'11px'} onClick={onIsOpen} size="xs">
              Cancel
            </Button>
          </Flex>
        </Flex>
      </Fade>{' '}
      <Modal
        blockScrollOnMount={false}
        isOpen={isOpen}
        onClose={() => undefined}
      >
        <ModalOverlay zIndex={'0'} />
      </Modal>
    </>
  );
}

export default PopupConfirmation;
