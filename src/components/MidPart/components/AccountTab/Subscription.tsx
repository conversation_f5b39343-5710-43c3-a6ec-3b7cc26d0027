import { Button, <PERSON>lex, <PERSON><PERSON>, <PERSON>, Text } from "@chakra-ui/react";
import { useState } from "react";
import useStateManager from "../../../../store/stateManager.hook";
import useCustomToast from "../../../shared/components/Toast/CustomToast.hook";
import {
  createCheckoutSessionPaddle,
  managePaddleSubscription,
  products,
} from "./paddle.utils";
import { HIGHLIGHTY_DOMAIN } from "../../../shared/utils/constants.consts";

function Subscription() {
  const {
    account: { account },
    subscription: { subscription },
  } = useStateManager();
  const customToast = useCustomToast();

  const [isPaddleLoading, setIsPaddleLoading] = useState(false);

  const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  });

  return (
    <Flex
      mt="2"
      bg="brand.purple"
      w="100%"
      rounded="md"
      p="2"
      pt="0"
      flexDir={"column"}
    >
      {account?.emailVerified ? null : (
        <Text
          fontWeight={600}
          fontSize="12px"
          color="red.500"
          textAlign={"center"}
        >
          Please verify your email address to access the free trial!
        </Text>
      )}
      {subscription?.isActive && !subscription.isCanceled ? (
        <>
          <Heading alignSelf={"center"} mt="1" fontSize={"13px"}>
            Change subscription settings
          </Heading>

          <Flex mt="2" flexDir={"column"} justify="space-evenly" w="100%">
            <Button
              size="sm"
              colorScheme={"orange"}
              onClick={() => managePaddleSubscription(subscription.updateUrl)}
            >
              <Text fontSize="12px">Modify subscription</Text>
            </Button>
            {subscription.isPaddle ? (
              <Button
                mt="2"
                size="xs"
                colorScheme={"red"}
                onClick={() => managePaddleSubscription(subscription.cancelUrl)}
              >
                <Text fontSize="12px">Cancel subscription</Text>
              </Button>
            ) : null}
          </Flex>
        </>
      ) : (
        <>
          <Heading alignSelf={"center"} mt="1" fontSize={"13px"}>
            Upgrade to Highlighty PRO!
          </Heading>
          {subscription?.isActive ? null : (
            <Heading alignSelf={"center"} mt="1" fontSize={"12px"}>
              7 days FREE trial!
            </Heading>
          )}

          {isPaddleLoading ? (
            <Text fontSize="12px" fontWeight={600}>
              We are going to open our payment page in a new tab. Please wait...
            </Text>
          ) : null}

          <Text
            color="brand.textColor"
            textAlign="center"
            fontSize="11px"
            textDecor={"none"}
            opacity="0.75"
            mt="1"
            mx="auto"
          >
            Check our
            <Link
              href={`${HIGHLIGHTY_DOMAIN}/pricing`}
              rel="noreferrer"
              target="_blank"
              color="#007bff"
            >
              {" "}
              pricing details.
            </Link>
          </Text>
          <Flex mt="2" justify="space-evenly" w="100%">
            {products.map((priceItem: any, i: number) => (
              <Button
                isDisabled={!account?.emailVerified}
                isLoading={isPaddleLoading}
                size="md"
                key={i}
                w="106px"
                colorScheme={i === 0 ? "whatsapp" : "telegram"}
                onClick={() =>
                  account?.emailVerified
                    ? createCheckoutSessionPaddle(
                        customToast,
                        setIsPaddleLoading,
                        account?.email,
                        priceItem?.id,
                        account.uid
                      )
                    : null
                }
              >
                <Flex flexDir={"column"}>
                  <Text fontSize="12px">
                    {"PRO"} / {priceItem.interval}
                  </Text>
                  <Text mt="1" fontSize="12px">
                    {formatter.format(priceItem.price / 100)}
                  </Text>
                </Flex>
              </Button>
            ))}
          </Flex>
          <Text
            color="brand.textColor"
            textAlign="center"
            fontSize="10px"
            textDecor={"none"}
            opacity="0.75"
            mt="1"
            fontStyle={"italic"}
          >
            *VAT excluded - final amount may vary*
          </Text>
        </>
      )}
    </Flex>
  );
}

export default Subscription;
