import useStateManager from "../../../../../store/stateManager.hook";
import MenuDropdownItem from "../../MenuDropdownItem";

function IsCompactViewFeature() {
  const { globalSettings } = useStateManager();

  return (
    <MenuDropdownItem
      onYes={() => globalSettings.onSetIsCompactView(true)}
      onNo={() => globalSettings.onSetIsCompactView(false)}
      isYes={globalSettings.isCompactVIew?.isOn}
      label="Compact view of query list"
      labelTooltip="Current column view will be replaced with a full-width row, smaller text, less padding for the query box."
    />
  );
}

export default IsCompactViewFeature;
