import { Flex, Text } from "@chakra-ui/react";
import { keyframes } from "@emotion/react";
import useStateManager from "../../../store/stateManager.hook";

function TrialMessage() {
  const {
    menuDropdown,
    menuView: { onSetMenuView },
    account: { account },
  } = useStateManager();
  const {
    account: { isLoading: isAccountLoading },
  } = useStateManager();

  function onCheckTrial() {
    onSetMenuView(3);
    if (!menuDropdown.isMenuOpen) {
      menuDropdown.onToggleMenu();
    }
  }

  const animationKeyframesL = keyframes`
  0% { transform: translateX(0px); }
  50% { transform: translateX(-5px); }  
  100% { transform: translateX(0px); }

`;

  const animationL = `${animationKeyframesL} 1s infinite`;

  const animationKeyframesR = keyframes`
  0% { transform: translateX(0px); }
  50% { transform: translateX(5px); }  
  100% { transform: translateX(0px); }

`;

  const animationR = `${animationKeyframesR} 1s infinite`;

  return isAccountLoading || account ? null : (
    <Flex textAlign="center" justify={"center"} w="100%" fontSize="12px">
      <Text animation={animationL}>👉 </Text>
      <Text
        px="1"
        onClick={onCheckTrial}
        cursor="pointer"
        fontWeight={600}
        mb="1"
        color="blackAlpha.700"
      >
        Get 7 days of PRO trial for FREE!
      </Text>
      <Text animation={animationR}>👈</Text>
    </Flex>
  );
}

export default TrialMessage;
