import useStateManager from '../../../../../store/stateManager.hook';
import MenuDropdownItem from '../../MenuDropdownItem';

function ScrollbarHighlighterFeature() {
  const { globalSettings } = useStateManager();

  // Default to true if the setting doesn't exist yet
  const isScrollbarHighlighterOn = globalSettings.scrollbarHighlighter?.isOn ?? true;

  return (
    <MenuDropdownItem
      onYes={() => globalSettings.onSetIsScrollbarHighlighter(true)}
      onNo={() => globalSettings.onSetIsScrollbarHighlighter(false)}
      isYes={isScrollbarHighlighterOn}
      label="Scrollbar markers"
      labelTooltip="Show markers in the scrollbar indicating where search matches are located"
    />
  );
}

export default ScrollbarHighlighterFeature;
