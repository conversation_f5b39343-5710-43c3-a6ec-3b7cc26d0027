import useStateManager from '../../../../../store/stateManager.hook';
import MenuDropdownItem from '../../MenuDropdownItem';

function QueryFoundFeature() {
  const { globalSettings } = useStateManager();

  return (
    <MenuDropdownItem
      onYes={() => globalSettings.onHasQueryFound(true)}
      onNo={() => globalSettings.onHasQueryFound(false)}
      isYes={globalSettings.queryFound.isOn}
      label="Query found alert"
    />
  );
}

export default QueryFoundFeature;
