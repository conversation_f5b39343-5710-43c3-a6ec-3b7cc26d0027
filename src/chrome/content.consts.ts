import { v4 as uuidv4 } from 'uuid';

export const punctuations = [
  ':',
  ';',
  '.',
  ',',
  '-',
  '–',
  '—',
  '‒',
  '_',
  '(',
  ')',
  '{',
  '}',
  '[',
  ']',
  '!',
  "'",
  '+',
  '=',
  '"',
];

export const dataHility = 'data-hility';

export const cssId = uuidv4();
export const cssHility = 'css-hility';

export const hilityTag = 'hility';

export const disableColorsState = {
  isTouched: false,
  isStyleDisabled: false,
};

export const isToastOn = { hasQueryFound: false, hasQueryNotFound: false };

export const isOnloadRender = { value: true };

export const blacklistObserverSites = ['youtube'];
