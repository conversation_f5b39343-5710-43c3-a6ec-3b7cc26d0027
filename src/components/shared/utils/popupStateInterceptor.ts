import {
  ExtensionViewState,
  GlobalSettings,
} from "../../../definitions/global.definition";

import { mapFreeGlobalSettings } from "../../../chrome/features/content.paygate";
import { paidFeatures } from "./constants.consts";
export function globalSettingsPopupInterceptor(
  globalSettings: GlobalSettings,
  isActive?: boolean
): GlobalSettings {
  return isActive ? globalSettings : mapFreeGlobalSettings(globalSettings);
}

export function extensionViewPopupInterceptor(
  extensionView: ExtensionViewState,
  isActive?: boolean
): ExtensionViewState {
  return isActive
    ? extensionView
    : { ...extensionView, ...paidFeatures.extensionView };
}

export function disablePopupVisualFeatures(
  isDisabled?: boolean,
  isSettingOn?: boolean
) {
  return isDisabled ? false : isSettingOn;
}

export function activatePaygate(
  i: number,
  isDisabled?: boolean,
  showedItems = 1
) {
  return isDisabled ? i <= showedItems : true;
}
