const ReplaceInFileWebpackPlugin = require("replace-in-file-webpack-plugin");
const CopyPlugin = require("copy-webpack-plugin");
const path = require("path");
const fs = require("fs");

module.exports = {
  webpack: {
    configure: (webpackConfig, { env, paths }) => {
      // Read MANIFEST_SOURCE_DIR from process.env
      const manifestSourceDir =
        process.env.REACT_APP_BROWSER_TYPE === "firefox" ? "" : "public";

      let plugins = [
        ...webpackConfig.plugins,
        new ReplaceInFileWebpackPlugin([
          // Your existing ReplaceInFileWebpackPlugin configurations
          {
            dir: "build",
            test: [/main.js(\.map)?$/],
            rules: [
              {
                search: "https://apis.google.com/js/api.js",
                replace: "",
              },
            ],
          },
          {
            dir: "build",
            test: [/index.html?$/],
            rules: [
              {
                search: `<script defer="defer" src="/static/js/content.js"></script>`,
                replace: "",
              },
              {
                search: `<script defer="defer" src="/static/js/background.js"></script>`,
                replace: "",
              },
              {
                search: `<noscript>You need to enable JavaScript to run this app.</noscript>`,
                replace: "",
              },
            ],
          },
        ]),
      ];

      // Check if the manifest file exists in the specified source directory
      const manifestFilePath = path.join(
        paths.appPath,
        manifestSourceDir,
        "manifest.json"
      );
      if (fs.existsSync(manifestFilePath)) {
        plugins.push(
          new CopyPlugin({
            patterns: [
              {
                from: manifestFilePath,
                to: path.join(paths.appPath, "build", "manifest.json"),
              },
            ],
          })
        );
      }

      return {
        ...webpackConfig,
        plugins,
        entry: {
          main: [
            env === "development" &&
              require.resolve("react-dev-utils/webpackHotDevClient"),
            paths.appIndexJs,
          ].filter(Boolean),
          content: paths.appSrc + "/chrome/content.ts",
          background: paths.appSrc + "/chrome/background.ts",
        },
        output: {
          ...webpackConfig.output,
          filename: "static/js/[name].js",
        },
        optimization: {
          ...webpackConfig.optimization,
          runtimeChunk: false,
        },
      };
    },
  },
};
