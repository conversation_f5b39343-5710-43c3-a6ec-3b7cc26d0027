import { Box, Flex, Icon, Tooltip } from "@chakra-ui/react";
import { <PERSON><PERSON><PERSON>, MouseEvent } from "react";
import { AiOutlineAim } from "react-icons/ai";
import { BsExclamationCircle, BsQuestionCircle, BsTypeUnderline } from "react-icons/bs";
import { QueryItem } from "../../../definitions/global.definition";
import useStateManager from "../../../store/stateManager.hook";
import { tooltipStyle } from "../../../styles/global.styles";
import {
  adaptiveFontColor,
  hslFormatter,
} from "../../shared/utils/color.utils";
interface Props {
  item: QueryItem;
  setNewItem: Dispatch<React.SetStateAction<QueryItem>>;
  setIsEdit: Dispatch<React.SetStateAction<boolean>>;
  hasTooltip: boolean;
}

function QueryItemBox({ hasTooltip, item, setNewItem, setIsEdit }: Props) {
  const {
    globalSettings: {
      navigationBar,
      isCompactVIew,
      isQueryClickInverted,
      isQueryClickDisabled,
      onRemoveItemQueryList,
      onNavigationBarWord,
    },
  } = useStateManager();

  function onLeftClick(e: MouseEvent<HTMLDivElement>) {
    e.preventDefault();

    if (isQueryClickDisabled?.isOn) return;

    onRemoveItemQueryList(item);
  }
  function onRightClick(e: MouseEvent<HTMLDivElement>) {
    e.preventDefault();

    if (isQueryClickDisabled?.isOn) return;

    setNewItem(item);
    setIsEdit(true);
  }
  function onFocusNavbarClick(e: MouseEvent<HTMLDivElement>) {
    e.preventDefault();
    onNavigationBarWord(item);
  }

  return (
    <Flex
      transition="all 200ms"
      fontSize={isCompactVIew.isOn ? "10px" : "12px"}
      fontWeight={500}
      bg={item.decorationType && item.decorationType !== "background" ? "white" : hslFormatter(item.colors.hsl)}
      minW={isCompactVIew.isOn ? "100%" : "49%"}
      color={item.decorationType && item.decorationType !== "background" ? "black" : adaptiveFontColor(item.colors.hsl)}
      justify={"center"}
      rounded="md"
      mb={isCompactVIew.isOn ? "0" : "1"}
      minH={isCompactVIew.isOn ? "27px" : "35px"}
      cursor="pointer"
      border="2px solid white"
      _hover={{
        border: "2px solid var(--chakra-colors-brand-inputBlueHover)",
        transform: isCompactVIew.isOn
          ? "scale(1.01)"
          : "scale(1.01) translateY(-1px)",
        shadow: "sm",
      }}
      position="relative"
      align="center"
      css={{
        "&::-webkit-scrollbar": {
          // width: '2px',
        },
      }}
      maxH="100px"
    >
      {hasTooltip ? (
        <Tooltip
          {...tooltipStyle}
          bg="brand.paragraphColor"
          w="170px"
          label={
            isQueryClickInverted.isOn
              ? `RIght click will delete the query. Left click will edit the query.`
              : `Left click will delete the query. Right click will edit the query.`
          }
        >
          <Box position={"absolute"} as="span" top="0px" right="2px">
            <Icon as={BsQuestionCircle} zIndex="100" />
          </Box>
        </Tooltip>
      ) : null}
      {hasTooltip && isQueryClickDisabled.isOn ? (
        <Tooltip
          {...tooltipStyle}
          bg="brand.paragraphColor"
          w="170px"
          label={"Clicks on queries have been disabled in the settings."}
        >
          <Box position={"absolute"} as="span" bottom="-3px" right="2px">
            <Icon as={BsExclamationCircle} zIndex="100" />
          </Box>
        </Tooltip>
      ) : null}

      <Flex
        textAlign={isCompactVIew.isOn ? "left" : "justify"}
        py={isCompactVIew.isOn ? "0" : "1"}
        px={isCompactVIew.isOn ? "4px" : "14px"}
        w="100%"
        h="100%"
        align="center"
        justify={isCompactVIew.isOn ? "flex-start" : "center"}
        onContextMenu={isQueryClickInverted.isOn ? onLeftClick : onRightClick}
        onClick={isQueryClickInverted.isOn ? onRightClick : onLeftClick}
        textOverflow={"ellipsis"}
        overflowX="scroll"
        overflowWrap={"break-word"}
        // wordBreak="break-all"
        style={{
          ...(item.decorationType && item.decorationType !== "background" ? {
            textDecoration: item.decorationType === "line-through" ? "line-through" :
                            item.decorationType === "overline" ? "overline" : "underline",
            textDecorationStyle: item.decorationType === "dotted-underline" ? "dotted" :
                                item.decorationType === "wavy-underline" ? "wavy" :
                                item.decorationType === "double-underline" ? "double" : "solid",
            textDecorationColor: hslFormatter(item.colors.hsl),
            textDecorationThickness: item.decorationThickness === "thin" ? "1px" :
                                    item.decorationThickness === "medium" ? "2px" :
                                    item.decorationThickness === "thick" ? "3px" :
                                    item.decorationThickness === "very-thick" ? "4px" :
                                    item.decorationThickness === "extra-thick" ? "5px" : "auto",
            background: "transparent",
          } : {})
        }}
      >
        {item.name}
      </Flex>
      {navigationBar.isOn ? (
        <Tooltip
          {...tooltipStyle}
          bg="brand.paragraphColor"
          w="170px"
          label={`Click to it in the navigation bar.`}
        >
          <Flex
            onClick={onFocusNavbarClick}
            position={"absolute"}
            as="span"
            top="0px"
            left="2px"
            zIndex="3"
          >
            <Icon my="auto" as={AiOutlineAim} />
          </Flex>
        </Tooltip>
      ) : null}

      {/* Decoration indicator */}
      {item.decorationType && item.decorationType !== "background" ? (
        <Tooltip
          {...tooltipStyle}
          bg="brand.paragraphColor"
          w="170px"
          label={`Using ${item.decorationType} decoration`}
        >
          <Flex
            position={"absolute"}
            as="span"
            bottom="0px"
            right="2px"
            zIndex="3"
          >
            <Icon my="auto" as={BsTypeUnderline} fontSize="10px" />
          </Flex>
        </Tooltip>
      ) : null}
    </Flex>
  );
}

export default QueryItemBox;
