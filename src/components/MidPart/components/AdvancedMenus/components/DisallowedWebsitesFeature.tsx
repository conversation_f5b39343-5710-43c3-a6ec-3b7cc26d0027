import { Textarea } from '@chakra-ui/react';
import { ChangeEvent } from 'react';
import useStateManager from '../../../../../store/stateManager.hook';
import { blueInputStyle } from '../../../../../styles/global.styles';
import MenuDropdownItem from '../../MenuDropdownItem';

function DisallowedWebsitesFeature() {
  const {
    blackList,
    globalSettings: { blacklistSites },
  } = useStateManager();

  function onChange(e: ChangeEvent<HTMLTextAreaElement>) {
    blackList.onSetBlackListSites(e.target.value);
  }
  return (
    <MenuDropdownItem
      labelTooltip="The extension will not work on these webpages. Separate the website domains by columns."
      isMore
      //   onMore={() => null}
      label="Disallowed websites"
      isYes={blacklistSites.isOn}
      onYes={() => blackList.onIsBlackListSite(true)}
      onNo={() => blackList.onIsBlackListSite(false)}
    >
      <Textarea
        onChange={onChange}
        p="1"
        maxH={'120px'}
        fontSize={'10.5px'}
        placeholder="google.com,linkedin.com"
        value={blacklistSites.websiteList}
        {...blueInputStyle}
      ></Textarea>
    </MenuDropdownItem>
  );
}

export default DisallowedWebsitesFeature;
