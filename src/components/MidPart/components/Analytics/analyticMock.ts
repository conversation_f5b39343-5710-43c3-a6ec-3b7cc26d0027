export const analyticMock: any = [
  {
    data: [
      {
        collectedData: {
          number: 42,
        },
        queryItem: {
          colors: {
            hex: "69CEA9",
            hsl: {
              a: 1,
              h: 158,
              l: 61,
              s: 51,
            },
            rgb: {
              a: 1,
              b: 169,
              g: 206,
              r: 105,
            },
          },
          id: "31c95b2d-0093-400c-970f-c1a75383d5e0",
          isInactive: false,
          isRegexp: false,
          name: "web",
        },
      },
      {
        collectedData: {
          number: 1,
        },
        queryItem: {
          colors: {
            hex: "AE5B9C",
            hsl: {
              a: 1,
              h: 313,
              l: 52,
              s: 34,
            },
            rgb: {
              a: 1,
              b: 156,
              g: 91,
              r: 174,
            },
          },
          id: "dd728666-44f8-48ce-a6e6-213dc54f8b90",
          isInactive: false,
          isRegexp: false,
          name: "feat",
        },
      },
      {
        collectedData: {
          number: 1,
        },
        queryItem: {
          colors: {
            hex: "7B3D49",
            hsl: {
              a: 1,
              h: 348,
              l: 36,
              s: 34,
            },
            rgb: {
              a: 1,
              b: 73,
              g: 61,
              r: 123,
            },
          },
          id: "936526a3-b88e-46f1-ae8a-8458a2df2e92",
          isInactive: false,
          isRegexp: false,
          name: "bug",
        },
      },
      {
        queryItem: {
          colors: {
            hex: "54CF66",
            hsl: {
              a: 1,
              h: 129,
              l: 57,
              s: 56,
            },
            rgb: {
              a: 1,
              b: 102,
              g: 207,
              r: 84,
            },
          },
          id: "e2d10a69-da9f-4c38-b172-64b65e1f8617",
          isInactive: false,
          isRegexp: false,
          name: "pedia",
        },
      },
      {
        queryItem: {
          colors: {
            hex: "B6CCA8",
            hsl: {
              a: 1,
              h: 97,
              l: 73,
              s: 26,
            },
            rgb: {
              a: 1,
              b: 168,
              g: 204,
              r: 182,
            },
          },
          id: "35f80bf8-3823-4d7f-8205-4be0731be25f",
          isInactive: false,
          isRegexp: false,
          name: "wiki",
        },
      },
    ],
    totalSum: 44,
    url: "https://www.w3schools.com/js/js_date_methods.asp",
    visitTime: "20/04/2024, 16:17",
  },
  {
    data: [
      {
        collectedData: {
          number: 11,
        },
        queryItem: {
          colors: {
            hex: "69CEA9",
            hsl: {
              a: 1,
              h: 158,
              l: 61,
              s: 51,
            },
            rgb: {
              a: 1,
              b: 169,
              g: 206,
              r: 105,
            },
          },
          id: "31c95b2d-0093-400c-970f-c1a75383d5e0",
          isInactive: false,
          isRegexp: false,
          name: "web",
        },
      },
      {
        queryItem: {
          colors: {
            hex: "AE5B9C",
            hsl: {
              a: 1,
              h: 313,
              l: 52,
              s: 34,
            },
            rgb: {
              a: 1,
              b: 156,
              g: 91,
              r: 174,
            },
          },
          id: "dd728666-44f8-48ce-a6e6-213dc54f8b90",
          isInactive: false,
          isRegexp: false,
          name: "feat",
        },
      },
      {
        queryItem: {
          colors: {
            hex: "7B3D49",
            hsl: {
              a: 1,
              h: 348,
              l: 36,
              s: 34,
            },
            rgb: {
              a: 1,
              b: 73,
              g: 61,
              r: 123,
            },
          },
          id: "936526a3-b88e-46f1-ae8a-8458a2df2e92",
          isInactive: false,
          isRegexp: false,
          name: "bug",
        },
      },
      {
        queryItem: {
          colors: {
            hex: "54CF66",
            hsl: {
              a: 1,
              h: 129,
              l: 57,
              s: 56,
            },
            rgb: {
              a: 1,
              b: 102,
              g: 207,
              r: 84,
            },
          },
          id: "e2d10a69-da9f-4c38-b172-64b65e1f8617",
          isInactive: false,
          isRegexp: false,
          name: "pedia",
        },
      },
      {
        queryItem: {
          colors: {
            hex: "B6CCA8",
            hsl: {
              a: 1,
              h: 97,
              l: 73,
              s: 26,
            },
            rgb: {
              a: 1,
              b: 168,
              g: 204,
              r: 182,
            },
          },
          id: "35f80bf8-3823-4d7f-8205-4be0731be25f",
          isInactive: false,
          isRegexp: false,
          name: "wiki",
        },
      },
    ],
    totalSum: 11,
    url: "https://www.google.com/search?q=get+date+js&rlz=1C5CHFA_enRO1068HU1068&oq=get+date+js&gs_lcrp=EgZjaHJvbWUyBggAEEUYOTIHCAEQABiABDIHCAIQABiABDIHCAMQABiABDIHCAQQABiABDIHCAUQABiABDIHCAYQABiABDIHCAcQABiABDIGCAgQRRhA0gEIMzEwMmowajeoAgCwAgA&sourceid=chrome&ie=UTF-8",
    visitTime: "20/04/2024, 16:17",
  },
  {
    data: [
      {
        collectedData: {
          number: 44,
        },
        queryItem: {
          colors: {
            hex: "69CEA9",
            hsl: {
              a: 1,
              h: 158,
              l: 61,
              s: 51,
            },
            rgb: {
              a: 1,
              b: 169,
              g: 206,
              r: 105,
            },
          },
          id: "31c95b2d-0093-400c-970f-c1a75383d5e0",
          isInactive: false,
          isRegexp: false,
          name: "web",
        },
      },
      {
        collectedData: {
          number: 6,
        },
        queryItem: {
          colors: {
            hex: "AE5B9C",
            hsl: {
              a: 1,
              h: 313,
              l: 52,
              s: 34,
            },
            rgb: {
              a: 1,
              b: 156,
              g: 91,
              r: 174,
            },
          },
          id: "dd728666-44f8-48ce-a6e6-213dc54f8b90",
          isInactive: false,
          isRegexp: false,
          name: "feat",
        },
      },
      {
        collectedData: {
          number: 1,
        },
        queryItem: {
          colors: {
            hex: "7B3D49",
            hsl: {
              a: 1,
              h: 348,
              l: 36,
              s: 34,
            },
            rgb: {
              a: 1,
              b: 73,
              g: 61,
              r: 123,
            },
          },
          id: "936526a3-b88e-46f1-ae8a-8458a2df2e92",
          isInactive: false,
          isRegexp: false,
          name: "bug",
        },
      },
      {
        collectedData: {
          number: 55,
        },
        queryItem: {
          colors: {
            hex: "54CF66",
            hsl: {
              a: 1,
              h: 129,
              l: 57,
              s: 56,
            },
            rgb: {
              a: 1,
              b: 102,
              g: 207,
              r: 84,
            },
          },
          id: "e2d10a69-da9f-4c38-b172-64b65e1f8617",
          isInactive: false,
          isRegexp: false,
          name: "pedia",
        },
      },
      {
        collectedData: {
          number: 418,
        },
        queryItem: {
          colors: {
            hex: "B6CCA8",
            hsl: {
              a: 1,
              h: 97,
              l: 73,
              s: 26,
            },
            rgb: {
              a: 1,
              b: 168,
              g: 204,
              r: 182,
            },
          },
          id: "35f80bf8-3823-4d7f-8205-4be0731be25f",
          isInactive: false,
          isRegexp: false,
          name: "wiki",
        },
      },
    ],
    totalSum: 524,
    url: "https://en.wikipedia.org/wiki/Wiki",
    visitTime: "20/04/2024, 16:09",
  },
  {
    data: [
      {
        collectedData: {
          number: 44,
        },
        queryItem: {
          colors: {
            hex: "69CEA9",
            hsl: {
              a: 1,
              h: 158,
              l: 61,
              s: 51,
            },
            rgb: {
              a: 1,
              b: 169,
              g: 206,
              r: 105,
            },
          },
          id: "31c95b2d-0093-400c-970f-c1a75383d5e0",
          isInactive: false,
          isRegexp: false,
          name: "web",
        },
      },
      {
        collectedData: {
          number: 6,
        },
        queryItem: {
          colors: {
            hex: "AE5B9C",
            hsl: {
              a: 1,
              h: 313,
              l: 52,
              s: 34,
            },
            rgb: {
              a: 1,
              b: 156,
              g: 91,
              r: 174,
            },
          },
          id: "dd728666-44f8-48ce-a6e6-213dc54f8b90",
          isInactive: false,
          isRegexp: false,
          name: "feat",
        },
      },
      {
        collectedData: {
          number: 1,
        },
        queryItem: {
          colors: {
            hex: "7B3D49",
            hsl: {
              a: 1,
              h: 348,
              l: 36,
              s: 34,
            },
            rgb: {
              a: 1,
              b: 73,
              g: 61,
              r: 123,
            },
          },
          id: "936526a3-b88e-46f1-ae8a-8458a2df2e92",
          isInactive: false,
          isRegexp: false,
          name: "bug",
        },
      },
      {
        collectedData: {
          number: 55,
        },
        queryItem: {
          colors: {
            hex: "54CF66",
            hsl: {
              a: 1,
              h: 129,
              l: 57,
              s: 56,
            },
            rgb: {
              a: 1,
              b: 102,
              g: 207,
              r: 84,
            },
          },
          id: "e2d10a69-da9f-4c38-b172-64b65e1f8617",
          isInactive: false,
          isRegexp: false,
          name: "pedia",
        },
      },
      {
        collectedData: {
          number: 418,
        },
        queryItem: {
          colors: {
            hex: "B6CCA8",
            hsl: {
              a: 1,
              h: 97,
              l: 73,
              s: 26,
            },
            rgb: {
              a: 1,
              b: 168,
              g: 204,
              r: 182,
            },
          },
          id: "35f80bf8-3823-4d7f-8205-4be0731be25f",
          isInactive: false,
          isRegexp: false,
          name: "wiki",
        },
      },
    ],
    totalSum: 524,
    url: "https://en.wikipedia.org/wiki/Wiki",
    visitTime: "20/04/2024, 16:03",
  },
  {
    data: [
      {
        collectedData: {
          number: 44,
        },
        queryItem: {
          colors: {
            hex: "69CEA9",
            hsl: {
              a: 1,
              h: 158,
              l: 61,
              s: 51,
            },
            rgb: {
              a: 1,
              b: 169,
              g: 206,
              r: 105,
            },
          },
          id: "31c95b2d-0093-400c-970f-c1a75383d5e0",
          isInactive: false,
          isRegexp: false,
          name: "web",
        },
      },
      {
        collectedData: {
          number: 6,
        },
        queryItem: {
          colors: {
            hex: "AE5B9C",
            hsl: {
              a: 1,
              h: 313,
              l: 52,
              s: 34,
            },
            rgb: {
              a: 1,
              b: 156,
              g: 91,
              r: 174,
            },
          },
          id: "dd728666-44f8-48ce-a6e6-213dc54f8b90",
          isInactive: false,
          isRegexp: false,
          name: "feat",
        },
      },
      {
        collectedData: {
          number: 1,
        },
        queryItem: {
          colors: {
            hex: "7B3D49",
            hsl: {
              a: 1,
              h: 348,
              l: 36,
              s: 34,
            },
            rgb: {
              a: 1,
              b: 73,
              g: 61,
              r: 123,
            },
          },
          id: "936526a3-b88e-46f1-ae8a-8458a2df2e92",
          isInactive: false,
          isRegexp: false,
          name: "bug",
        },
      },
      {
        collectedData: {
          number: 55,
        },
        queryItem: {
          colors: {
            hex: "54CF66",
            hsl: {
              a: 1,
              h: 129,
              l: 57,
              s: 56,
            },
            rgb: {
              a: 1,
              b: 102,
              g: 207,
              r: 84,
            },
          },
          id: "e2d10a69-da9f-4c38-b172-64b65e1f8617",
          isInactive: false,
          isRegexp: false,
          name: "pedia",
        },
      },
      {
        collectedData: {
          number: 418,
        },
        queryItem: {
          colors: {
            hex: "B6CCA8",
            hsl: {
              a: 1,
              h: 97,
              l: 73,
              s: 26,
            },
            rgb: {
              a: 1,
              b: 168,
              g: 204,
              r: 182,
            },
          },
          id: "35f80bf8-3823-4d7f-8205-4be0731be25f",
          isInactive: false,
          isRegexp: false,
          name: "wiki",
        },
      },
    ],
    totalSum: 524,
    url: "https://en.wikipedia.org/wiki/Wiki",
    visitTime: "20/04/2024, 16:03",
  },
  {
    data: [
      {
        collectedData: {
          number: 6,
        },
        queryItem: {
          colors: {
            hex: "AE5B9C",
            hsl: {
              a: 1,
              h: 313,
              l: 52,
              s: 34,
            },
            rgb: {
              a: 1,
              b: 156,
              g: 91,
              r: 174,
            },
          },
          id: "dd728666-44f8-48ce-a6e6-213dc54f8b90",
          isInactive: false,
          isRegexp: false,
          name: "feat",
        },
      },
      {
        collectedData: {
          number: 1,
        },
        queryItem: {
          colors: {
            hex: "7B3D49",
            hsl: {
              a: 1,
              h: 348,
              l: 36,
              s: 34,
            },
            rgb: {
              a: 1,
              b: 73,
              g: 61,
              r: 123,
            },
          },
          id: "936526a3-b88e-46f1-ae8a-8458a2df2e92",
          isInactive: false,
          isRegexp: false,
          name: "bug",
        },
      },
      {
        collectedData: {
          number: 55,
        },
        queryItem: {
          colors: {
            hex: "54CF66",
            hsl: {
              a: 1,
              h: 129,
              l: 57,
              s: 56,
            },
            rgb: {
              a: 1,
              b: 102,
              g: 207,
              r: 84,
            },
          },
          id: "e2d10a69-da9f-4c38-b172-64b65e1f8617",
          isInactive: false,
          isRegexp: false,
          name: "pedia",
        },
      },
      {
        collectedData: {
          number: 418,
        },
        queryItem: {
          colors: {
            hex: "B6CCA8",
            hsl: {
              a: 1,
              h: 97,
              l: 73,
              s: 26,
            },
            rgb: {
              a: 1,
              b: 168,
              g: 204,
              r: 182,
            },
          },
          id: "35f80bf8-3823-4d7f-8205-4be0731be25f",
          isInactive: false,
          isRegexp: false,
          name: "wiki",
        },
      },
    ],
    totalSum: 480,
    url: "https://en.wikipedia.org/wiki/Wiki",
    visitTime: "20/04/2024, 16:03",
  },
  {
    data: [
      {
        collectedData: {
          number: 1,
        },
        queryItem: {
          colors: {
            hex: "7B3D49",
            hsl: {
              a: 1,
              h: 348,
              l: 36,
              s: 34,
            },
            rgb: {
              a: 1,
              b: 73,
              g: 61,
              r: 123,
            },
          },
          id: "936526a3-b88e-46f1-ae8a-8458a2df2e92",
          isInactive: false,
          isRegexp: false,
          name: "bug",
        },
      },
      {
        collectedData: {
          number: 55,
        },
        queryItem: {
          colors: {
            hex: "54CF66",
            hsl: {
              a: 1,
              h: 129,
              l: 57,
              s: 56,
            },
            rgb: {
              a: 1,
              b: 102,
              g: 207,
              r: 84,
            },
          },
          id: "e2d10a69-da9f-4c38-b172-64b65e1f8617",
          isInactive: false,
          isRegexp: false,
          name: "pedia",
        },
      },
      {
        collectedData: {
          number: 418,
        },
        queryItem: {
          colors: {
            hex: "B6CCA8",
            hsl: {
              a: 1,
              h: 97,
              l: 73,
              s: 26,
            },
            rgb: {
              a: 1,
              b: 168,
              g: 204,
              r: 182,
            },
          },
          id: "35f80bf8-3823-4d7f-8205-4be0731be25f",
          isInactive: false,
          isRegexp: false,
          name: "wiki",
        },
      },
    ],
    totalSum: 474,
    url: "https://en.wikipedia.org/wiki/Wiki",
    visitTime: "20/04/2024, 16:03",
  },
  {
    data: [
      {
        collectedData: {
          number: 55,
        },
        queryItem: {
          colors: {
            hex: "54CF66",
            hsl: {
              a: 1,
              h: 129,
              l: 57,
              s: 56,
            },
            rgb: {
              a: 1,
              b: 102,
              g: 207,
              r: 84,
            },
          },
          id: "e2d10a69-da9f-4c38-b172-64b65e1f8617",
          isInactive: false,
          isRegexp: false,
          name: "pedia",
        },
      },
      {
        collectedData: {
          number: 418,
        },
        queryItem: {
          colors: {
            hex: "B6CCA8",
            hsl: {
              a: 1,
              h: 97,
              l: 73,
              s: 26,
            },
            rgb: {
              a: 1,
              b: 168,
              g: 204,
              r: 182,
            },
          },
          id: "35f80bf8-3823-4d7f-8205-4be0731be25f",
          isInactive: false,
          isRegexp: false,
          name: "wiki",
        },
      },
    ],
    totalSum: 473,
    url: "https://en.wikipedia.org/wiki/Wiki",
    visitTime: "20/04/2024, 16:03",
  },
];
