import useStateManager from "../../../../../store/stateManager.hook";
import MenuDropdownItem from "../../MenuDropdownItem";

function DiacriticSensFeature() {
  const { globalSettings } = useStateManager();

  return (
    <MenuDropdownItem
      onYes={() => globalSettings.onIsDiacriticSens(true)}
      onNo={() => globalSettings.onIsDiacriticSens(false)}
      isYes={globalSettings.diacriticSens.isOn}
      label="Diacritic sensitivity"
      labelTooltip="When enabled, the search will be case sensitive and will take into account the diacritics."
    />
  );
}

export default DiacriticSensFeature;
