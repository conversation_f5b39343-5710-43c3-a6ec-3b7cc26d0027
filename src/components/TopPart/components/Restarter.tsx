import { Button, Flex, SlideFade, Text } from "@chakra-ui/react";
import { motion } from "framer-motion";
import useStateManager from "../../../store/stateManager.hook";
import { alertAnimation, colors } from "../../../styles/global.styles";

function Restarter() {
  const {
    freshRestart: { onSetHasClickedRestart, hasClickedRestart },
  } = useStateManager();

  if (hasClickedRestart) return null;
  return (
    <SlideFade in={!hasClickedRestart}>
      <Flex
        as={motion.div}
        bg="red.300"
        p="1"
        justify={"center"}
        rounded="md"
        align="center"
        flexDir={"column"}
        w="89%"
        mx="auto"
        mb="2"
        shadow={"lg"}
        animation={alertAnimation}
        _hover={{
          animationPlayState: "paused",
        }}
        userSelect="none"
        color="white"
      >
        <Text fontWeight={"500"} textAlign={"center"} fontSize={"11px"}>
          To activate the extension in your browser, please refresh all the open
          tabs.
        </Text>

        <Button
          mt="1"
          bg={colors.brand.purpleFull}
          _hover={{
            bg: colors.brand.purple,
          }}
          _active={{
            bg: colors.brand.purpleFull,
          }}
          size="xs"
          fontSize={"11px"}
          onClick={onSetHasClickedRestart}
        >
          Sure, I'll do that!
        </Button>
      </Flex>
    </SlideFade>
  );
}

export default Restarter;
