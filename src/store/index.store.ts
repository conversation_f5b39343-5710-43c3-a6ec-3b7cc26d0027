import { configureStore } from '@reduxjs/toolkit';
import accountSubscriptionSlice from './accountSubscription.slice';
import extensionViewSlice from './extensionView.slice';
import globalSettingsSlice from './globalSettings.slice';

export const store = configureStore({
  reducer: {
    extensionViewSlice: extensionViewSlice,
    globalSettingsSlice: globalSettingsSlice,
    accountSubscriptionSlice: accountSubscriptionSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
      immutableCheck: false,
    }),
});

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
// Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}
export type AppDispatch = typeof store.dispatch;
