import { Flex } from "@chakra-ui/react";
import FoundSizeIncreaseFeature from "./components/FoundSizeIncreaseFeature.premium";

import PremiumTooltip from "../../../shared/components/PremiumTooltip/PremiumTooltip";
import FeatureGroupingAccordion from "../FeatureGroupingAccordion";
import AllowedWebsitesFeature from "./components/AllowedWebsitesFeature";
import BulkQueryUpload from "./components/BulkQueryUpload";
import CaseSensitivityFeature from "./components/CaseSensitivityFeature";
import CompleteSearchFeature from "./components/CompleteSearchFeature";
import DiacriticSensFeature from "./components/DiacriticSensFeature";
import DisallowedWebsitesFeature from "./components/DisallowedWebsitesFeature";
import DownloadUploadQueryList from "./components/DownloadUploadQueryList";
import IframeSearchFeature from "./components/IframeSearchFeature";
import SaveQueryListFeature from "./components/SaveQueryListFeature";
import SplitSearchFeature from "./components/SplitSearchFeature";

function AdvancedMenus() {
  return (
    <Flex flexDir={"column"}>
      <PremiumTooltip>
        <FoundSizeIncreaseFeature />
      </PremiumTooltip>

      <FeatureGroupingAccordion title="Query list features">
        <BulkQueryUpload />

        <DownloadUploadQueryList />

        <PremiumTooltip label="More than 2 lists are 🔐PRO feature.">
          <SaveQueryListFeature />
        </PremiumTooltip>
      </FeatureGroupingAccordion>
      <FeatureGroupingAccordion title="White/blacklist features">
        <AllowedWebsitesFeature />

        <DisallowedWebsitesFeature />
      </FeatureGroupingAccordion>

      <FeatureGroupingAccordion title="Searching options">
        <CaseSensitivityFeature />

        <DiacriticSensFeature />

        <SplitSearchFeature />

        <CompleteSearchFeature />

        <IframeSearchFeature />
      </FeatureGroupingAccordion>
    </Flex>
  );
}

export default AdvancedMenus;
