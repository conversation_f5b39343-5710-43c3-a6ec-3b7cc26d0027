import { useEffect } from "react";
import useStateManager from "../../../store/stateManager.hook";
import {
  getAuthStateCache,
  getCurrentDate,
  setAuthStateCache,
} from "../../MidPart/components/AccountTab/cachedAccount.utils";
import { authStateChangedObserver } from "../../MidPart/components/AccountTab/firebase.utils";

function useInitAccountSubscription() {
  const {
    account: { onSetAccount, onSetIsAccountLoading },
    subscription: { onSetSubscription },
  } = useStateManager();

  useEffect(() => {
    /// IDE BE VAN CACHELVE EZ A LOADING
    const fetchData = async () => {
      const authStateCached = await getAuthStateCache(
        onSetAccount,
        onSetSubscription
      );

      if (!authStateCached?.date) {
        onSetIsAccountLoading(false);

        return;
      }

      if (authStateCached?.date === getCurrentDate()) {
        onSetAccount(authStateCached.user);
        onSetSubscription(authStateCached.paddle);
        onSetIsAccountLoading(false);

        return;
      }

      // ez fut le amikor az app eloszor betolt - persistent login/logout
      authStateChangedObserver(
        onSetAccount,
        onSetIsAccountLoading,
        onSetSubscription,
        setAuthStateCache
      );
    };

    fetchData();
  }, []);
}

export default useInitAccountSubscription;
