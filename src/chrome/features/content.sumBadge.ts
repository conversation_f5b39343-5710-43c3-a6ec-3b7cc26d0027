import { GlobalSettings } from '../../definitions/global.definition';
import { ChromeMessage, Sender } from '../../types';
import { sendMessageTo } from '../chrome.utils';
import { hilityTag } from '../content.consts';

export const showFoundCountOnIcon = (globalSettings: GlobalSettings) => {
  const allFinds = document.getElementsByTagName(hilityTag)?.length;

  const bgMessage: ChromeMessage = {
    from: Sender.SummaryBadge,
    message:
      globalSettings?.badgeFoundSum?.isOn &&
      globalSettings?.isExtensionOn &&
      allFinds
        ? allFinds
        : '',
  };
  sendMessageTo(bgMessage);
};

export const showFoundCountOnIconCleanup = () => {
  const bgMessage: ChromeMessage = {
    from: Sender.SummaryBadge,
    message: '',
  };

  sendMessageTo(bgMessage);
};
