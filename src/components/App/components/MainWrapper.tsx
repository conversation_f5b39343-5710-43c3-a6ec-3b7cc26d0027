import { Flex } from '@chakra-ui/react';
import { ReactNode } from 'react';
import { extensionBackground } from '../../../styles/global.styles';

interface Props {
  children: ReactNode;
}

function MainWrapper({ children }: Props) {
  return (
    <Flex
      mx="auto"
      justify={'center'}
      color="brand.paragraphColor"
      bg={extensionBackground}
      w="270px"
      flexDir="column"
    >
      {children}
    </Flex>
  );
}

export default MainWrapper;
