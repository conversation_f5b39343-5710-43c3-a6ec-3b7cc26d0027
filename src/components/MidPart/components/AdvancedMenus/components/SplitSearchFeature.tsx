import useStateManager from "../../../../../store/stateManager.hook";
import MenuDropdownItem from "../../MenuDropdownItem";

function SplitSearchFeature() {
  const { globalSettings } = useStateManager();

  return (
    <MenuDropdownItem
      onYes={() => globalSettings.onIsSplitSearch(true)}
      onNo={() => globalSettings.onIsSplitSearch(false)}
      isYes={globalSettings.splitSearch.isOn}
      label="Split search"
      labelTooltip="Queries made up of multiple words will be highlighted individually as well as together."
    />
  );
}

export default SplitSearchFeature;
