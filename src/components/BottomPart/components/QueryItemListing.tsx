import { Flex } from '@chakra-ui/react';
import { Dispatch } from 'react';
import { QueryItem } from '../../../definitions/global.definition';
import useStateManager from '../../../store/stateManager.hook';
import { activatePaygate } from '../../shared/utils/popupStateInterceptor';
import QueryItemBox from './QueryItemBox';

interface Props {
  setNewItem: Dispatch<React.SetStateAction<QueryItem>>;
  setIsEdit: Dispatch<React.SetStateAction<boolean>>;
}

function QueryItemListing({ setNewItem, setIsEdit }: Props) {
  const {
    globalSettings: {
      queryListState: { selectedList },
    },
    subscription: { subscription },
  } = useStateManager();
  return (
    <Flex
      maxH="250px"
      overflowY="scroll"
      justify="space-between"
      flexWrap="wrap"
      mt="2"
      px="2"
    >
      {selectedList.itemList.map((item, i) =>
        activatePaygate(i, !subscription?.isActive, 4) ? (
          <QueryItemBox
            hasTooltip={i === 0}
            setIsEdit={setIsEdit}
            key={item.id}
            setNewItem={setNewItem}
            item={item}
          />
        ) : null
      )}
    </Flex>
  );
}

export default QueryItemListing;
