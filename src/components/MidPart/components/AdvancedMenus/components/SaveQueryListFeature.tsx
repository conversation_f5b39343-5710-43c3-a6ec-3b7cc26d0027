import { Flex, Icon, Input, Select, Text } from '@chakra-ui/react';
import { ChangeEvent, FormEvent, useEffect, useRef, useState } from 'react';
import { AiOutlinePlusCircle } from 'react-icons/ai';
import { BsTrash } from 'react-icons/bs';
import { FiEdit2 } from 'react-icons/fi';
import { IoSaveOutline } from 'react-icons/io5';
import useStateManager from '../../../../../store/stateManager.hook';
import { blueInputStyle } from '../../../../../styles/global.styles';
import PopupConfirmation from '../../../../shared/components/PopupConfirmation/PopupConfirmation';
import useCustomToast from '../../../../shared/components/Toast/CustomToast.hook';
import { activatePaygate } from '../../../../shared/utils/popupStateInterceptor';

function SaveQueryListFeature({ isDisabled }: { isDisabled?: boolean }) {
  // const [isMoreOpen, setIsMoreOpen] = useState(false);
  const customToast = useCustomToast();
  const {
    globalSettings: {
      onAddQueryList,
      onRemoveQueryList,
      onEditQueryList,
      onSelectQueryList,
      queryListState: { queryList, selectedList },
    },
  } = useStateManager();

  const [isNewList, setIsNewList] = useState(false);
  const [isEditList, setIsEditList] = useState(false);
  const [newListName, setNewListName] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);

  const [isDeleteModal, setIsDeleteModal] = useState(false);

  function onDeleteList() {
    setIsDeleteModal(false);
    onRemoveQueryList();
  }

  function onDeleteModalOpen() {
    if (isNewList || isEditList) {
      resetInput();
      return;
    }
    setIsDeleteModal(true);
  }

  function onChangeInput(e: ChangeEvent<HTMLInputElement>) {
    setNewListName(e.target.value.trim());
  }

  useEffect(() => {
    if (document) inputRef.current?.focus();

    return () => {};
  }, [isNewList, isEditList]);

  function resetInput() {
    setIsNewList(false);
    setNewListName('');
    setIsEditList(false);
  }

  function onSaveList(e?: FormEvent) {
    e?.preventDefault();

    if (!newListName?.length) {
      return customToast(`List name required.`, 'error');
    }

    if (queryList.filter((listItem) => listItem.name === newListName)?.length) {
      return customToast(`List name already exists.`, 'error');
    }

    if (isNewList) {
      onAddQueryList(newListName);
    }

    if (isEditList) {
      onEditQueryList(newListName);
    }

    resetInput();
  }

  return (
    <Flex
      rounded="md"
      transition="all 250ms"
      mb="1"
      flexDir="column"
      pt="3px"
      px="3px"
    >
      {isDeleteModal ? (
        <PopupConfirmation
          isOpen={isDeleteModal}
          selectedList={selectedList}
          onIsOpen={() => setIsDeleteModal((state) => !state)}
          onDeleteList={onDeleteList}
        />
      ) : null}
      <Flex pos="relative" align="center">
        <Text userSelect={'none'} fontSize={'xs'}>
          Saved query lists:
        </Text>
      </Flex>
      <Flex
        onSubmit={onSaveList}
        as="form"
        h="26px"
        justify="space-between"
        align="center"
      >
        {isNewList || isEditList ? (
          <Input
            h="25px"
            ref={inputRef}
            {...blueInputStyle}
            onChange={onChangeInput}
            w="65%"
            value={newListName}
            placeholder="name of the list"
          />
        ) : (
          <Select
            value={selectedList.id}
            onChange={(e) => onSelectQueryList(e.target.value)}
            h="26px"
            {...blueInputStyle}
            w="65%"
          >
            {queryList.map((listItem, i: number) =>
              activatePaygate(i, isDisabled) ? (
                <option value={listItem.id} key={listItem.id}>
                  {listItem.name}
                </option>
              ) : null
            )}
          </Select>
        )}

        <Icon
          transition="all 100ms"
          _hover={{ color: 'brand.purpleFull' }}
          as={AiOutlinePlusCircle}
          cursor={
            isDisabled && queryList?.length >= 2
              ? 'not-allowed'
              : isNewList
              ? 'unset'
              : 'pointer'
          }
          opacity={isNewList ? '0' : '1'}
          onClick={() =>
            isDisabled && queryList?.length >= 2
              ? undefined
              : setIsNewList(true)
          }
        />
        <Icon
          _hover={{ color: 'brand.yesButtonColor' }}
          as={IoSaveOutline}
          onClick={onSaveList}
          cursor={isNewList || isEditList ? 'pointer' : 'unset'}
          opacity={isNewList || isEditList ? '1' : '0'}
        />

        <Icon
          transition="all 100ms"
          _hover={{ color: 'brand.blueFull' }}
          as={FiEdit2}
          cursor={isNewList ? 'unset' : 'pointer'}
          opacity={isNewList ? '0' : '1'}
          onClick={() => {
            setIsEditList(true);
            setNewListName(selectedList.name);
          }}
        />

        <Icon
          onClick={onDeleteModalOpen}
          _hover={{ color: 'brand.noButtonColor' }}
          cursor={'pointer'}
          as={BsTrash}
        />
      </Flex>
    </Flex>
  );
}

export default SaveQueryListFeature;
