import { Flex, keyframes, Text } from "@chakra-ui/react";
import { useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { QueryItem } from "../../definitions/global.definition";
import useStateManager from "../../store/stateManager.hook";
import NavigationBarBottomPart from "../MidPart/components/BasicMenus/components/NavigationBarFeature/NavigationBarBottomPart";

import { getDefaultColors } from "../shared/utils/color.utils";
import QueryAdder from "./components/QueryAdder";
import QueryItemListing from "./components/QueryItemListing";
import { HIGHLIGHTY_DOMAIN } from "../shared/utils/constants.consts";

export const DEFAULT_QUERY_ITEM: QueryItem = {
  name: "",
  colors: getDefaultColors(),
  id: uuidv4(),
  isInactive: false,
  isRegexp: false,
  decorationType: 'background',
  decorationThickness: 'medium',
};

function BottomPart() {
  const [newItem, setNewItem] = useState<QueryItem>(DEFAULT_QUERY_ITEM);
  const [isEdit, setIsEdit] = useState(false);
  const { globalSettings } = useStateManager();

  const animationKeyframes = keyframes`
  0% { opacity: 0; }
  100% { opacity: 0.6; }
`;

  const animation = `${animationKeyframes} .3s`;

  return (
    <Flex flexDir={"column"} pos="relative">
      {globalSettings.isExtensionOn ? null : (
        <Flex
          pos="absolute"
          w="100%"
          rounded="md"
          zIndex={"99"}
          bg="gray.100"
          h="calc(100% - 35px)"
          opacity=".6"
          animation={animation}
        ></Flex>
      )}
      <Text my="1" textAlign={"center"} fontSize={12}>
        Find and highlight multiple words on the web.
      </Text>
      <NavigationBarBottomPart />
      <QueryAdder
        setIsEdit={setIsEdit}
        isEdit={isEdit}
        setNewItem={setNewItem}
        newItem={newItem}
      />
      <QueryItemListing setIsEdit={setIsEdit} setNewItem={setNewItem} />
    </Flex>
  );
}

export default BottomPart;
