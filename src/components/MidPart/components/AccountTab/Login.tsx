import { Button, Flex, Heading, Input, Text } from "@chakra-ui/react";
import { ChangeEvent, useState } from "react";

import useStateManager from "../../../../store/stateManager.hook";
import { blueInputStyle } from "../../../../styles/global.styles";
import useCustomToast from "../../../shared/components/Toast/CustomToast.hook";
import {
  resetPassword,
  signUpWithEmail,
  singInWithEmail,
} from "./firebase.utils";
import PrivacyTerms from "./PrivacyTerms";

function Login() {
  const [registerData, setRegisterData] = useState({
    email: process.env.REACT_APP_USERNAME ?? "",
    password: process.env.REACT_APP_PASSWORD ?? "",
  });
  const [isPasswordReset, setIsPasswordReset] = useState(false);
  const [isRegister, setIsRegister] = useState(false);
  const customToast = useCustomToast();
  const {
    account: { onSetIsAccountLoading, onSetAccount },
    subscription: { onSetSubscription },
  } = useStateManager();

  function onChangeInput(e: ChangeEvent<HTMLInputElement>) {
    switch (e.target.name) {
      case "email":
        setRegisterData((state) => ({ ...state, email: e.target.value }));
        break;
      case "password":
        setRegisterData((state) => ({ ...state, password: e.target.value }));
        break;
    }
  }

  function onSignupWithEmail(e: any) {
    e.preventDefault();
    signUpWithEmail(
      registerData.email,
      registerData.password,
      customToast,
      onSetIsAccountLoading,
      onSetAccount,
      onSetSubscription
    );
  }

  function onLoginWithEmail(e: any) {
    e.preventDefault();

    singInWithEmail(
      registerData.email,
      registerData.password,
      customToast,
      onSetIsAccountLoading,
      onSetAccount,
      onSetSubscription
    );
  }
  function onResetPassword(e: any) {
    e.preventDefault();
    resetPassword(registerData.email, customToast);
  }

  return (
    <Flex w="100%" align="center" flexDir={"column"}>
      <Heading mt="0" textTransform={"uppercase"} fontSize={"13px"}>
        Login / Register
      </Heading>
      <Flex wrap={"wrap"} mt="4" w="100%" justify={"space-evenly"}>
        {/* <Button onClick={signInWithGithub} size="xs">
          Github
        </Button> */}
        {/* <Button onClick={signInWithGoogle} size="xs">
          Gmail
        </Button> */}
        {isPasswordReset || isRegister ? (
          <Button
            alignSelf={"flex-start"}
            justifySelf="flex-start"
            onClick={() => {
              setIsRegister(false);
              setIsPasswordReset(false);
            }}
            size="xs"
          >
            Back to login
          </Button>
        ) : null}
      </Flex>
      <Flex w="70%" as="form" flexDir={"column"}>
        <Input
          h="25px"
          {...blueInputStyle}
          onChange={onChangeInput}
          w="100%"
          value={registerData.email}
          type={"email"}
          placeholder="email"
          mt="2"
          mb="1"
          name="email"
          isRequired
        />
        {isPasswordReset ? null : (
          <Input
            h="25px"
            {...blueInputStyle}
            onChange={onChangeInput}
            w="100%"
            type={"password"}
            value={registerData.password}
            placeholder="password"
            mb="1"
            name="password"
            isRequired
          />
        )}
        {isPasswordReset ? (
          <Button
            onClick={onResetPassword}
            type="submit"
            colorScheme={"whatsapp"}
            size="xs"
          >
            Reset Password
          </Button>
        ) : null}
        {isRegister ? <PrivacyTerms /> : null}

        {isRegister ? (
          <Button
            display={isPasswordReset ? "none" : "block"}
            onClick={onSignupWithEmail}
            type="submit"
            colorScheme={"telegram"}
            size="xs"
          >
            Sign me up!
          </Button>
        ) : (
          <Button
            display={isPasswordReset ? "none" : "block"}
            onClick={onLoginWithEmail}
            type="submit"
            colorScheme={"whatsapp"}
            size="xs"
          >
            Log me in!
          </Button>
        )}
      </Flex>

      {isRegister ? null : (
        <Button
          colorScheme={"pink"}
          onClick={() => {
            setIsRegister(true);
            setIsPasswordReset(false);
          }}
          mt="2"
          size="xs"
          fontSize={"10px"}
        >
          No account? Register.
        </Button>
      )}
      <Text
        display={isRegister ? "none" : "block"}
        onClick={() => setIsPasswordReset((state) => !state)}
        textAlign="center"
        fontSize="10px"
        textDecor={"none"}
        opacity="0.75"
        my="1"
        as="b"
        color="#007bff"
        cursor={"pointer"}
      >
        Forgot password?
      </Text>
    </Flex>
  );
}

export default Login;
