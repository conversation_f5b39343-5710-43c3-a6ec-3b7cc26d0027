import { MarkAccuracy } from "mark.js";
import { hslFormatter } from "../../components/shared/utils/color.utils";
import { GlobalSettings, QueryItem } from "../../definitions/global.definition";
import Mark from "../../markJs/src/vanilla";
import {
  cssHility,
  cssId,
  dataHility,
  hilityTag,
  punctuations,
} from "../content.consts";
import {
  initScrollbarHighlighter,
  updateScrollbarMarkers,
  clearScrollbarMarkers,
} from "./content.scrollbarHighlighter";
import {
  addTextDecorationFallbacks,
  applyDecorationToMark,
  getDecorationCSS
} from "./utils/decoration.utils";

export function doMarkSearch(
  settings: GlobalSettings,
  isObserver?: boolean,
  queryItem?: QueryItem
) {
  const body = document.body;

  if (!settings || !body) return;

  // console.log("Starting doMarkSearch with settings:", JSON.stringify(settings));
  // if (queryItem) {
  // console.log("Single queryItem:", JSON.stringify(queryItem));
  // }

  // Initialize scrollbar highlighter
  initScrollbarHighlighter(settings);

  // Add browser compatibility fallbacks for text decorations
  addTextDecorationFallbacks();

  const wa = new Mark(body as HTMLBodyElement);

  if (queryItem?.id) {
    const options = {
      exclude: [
        "hility-success",
        "hility-error",
        hilityTag,
        "textarea",
        "input",
      ],
    };
    doSingleHighlight(queryItem, settings, wa, options);

    // Update scrollbar markers after highlighting
    setTimeout(() => updateScrollbarMarkers(), 100);
    return;
  }

  if (!isObserver) {
    wa.unmark({
      iframes: true,
      done: function () {
        const options = {
          exclude: ["hility-success", "hility-error", "textarea", "input"],
        };

        doHighlight(settings, wa, options);

        // Update scrollbar markers after highlighting
        setTimeout(() => updateScrollbarMarkers(), 100);
      },
    });
  } else {
    const options = {
      exclude: [
        "hility-success",
        "hility-error",
        hilityTag,
        "textarea",
        "input",
      ],
      isObserver: isObserver,
    };

    doHighlight(settings, wa, options);

    // Update scrollbar markers after highlighting
    setTimeout(() => updateScrollbarMarkers(), 100);
  }
}

export function generateHilityStylesheet(
  queryItem: QueryItem,
  isFoundSizeIncrease: boolean
) {
  if (!queryItem) return;

  // Log the queryItem to debug
  // console.log("Generating stylesheet for queryItem:", JSON.stringify(queryItem));

  const doesExist = document.getElementById(queryItem.id);

  // Get the color for this query item
  const color = hslFormatter(queryItem.colors.hsl);

  // Create a specific color rule for this query item
  const colorRule =
    queryItem.decorationType && queryItem.decorationType !== "background"
      ? `
  /* Specific color rule for this query item */
  [${dataHility}="${queryItem.id}"] {
    text-decoration-color: ${color} !important;
    border-bottom-color: ${color} !important;
    border-top-color: ${color} !important;
  }

  /* Higher specificity rule to ensure color is applied */
  [${dataHility}="${queryItem.id}"][data-decoration-type="${queryItem.decorationType}"] {
    text-decoration-color: ${color} !important;
    border-bottom-color: ${color} !important;
    border-top-color: ${color} !important;
  }
  `
      : "";

  const addedStyle = `
  [${dataHility}="${queryItem.id}"]{
    ${getDecorationCSS(queryItem)}
    ${isFoundSizeIncrease ? "font-size: 25px;" : ""}
  }
  ${colorRule}`;

  if (doesExist) {
    doesExist.textContent = addedStyle;
    return;
  }

  const stylesheet = document.createElement("style");
  stylesheet.setAttribute("type", "text/css");
  stylesheet.setAttribute("id", queryItem.id);
  stylesheet.setAttribute(cssHility, cssId);
  stylesheet.textContent = addedStyle;

  document.head.appendChild(stylesheet);
}

const doHighlight = (
  settings: GlobalSettings,
  wa: Mark,
  options?: {
    exclude?: string[];
    isObserver?: boolean;
  }
) => {
  // settings.queryListState.selectedList.itemList.forEach((item) =>
  for (const item of settings.queryListState.selectedList.itemList) {
    //FASTER

    const basicParams = {
      element: hilityTag,
      iframes: settings?.iframeSearch.isOn,
      exclude: options?.exclude,
      acrossElements: true,
      each: (mark: any) => {
        // Log the item being processed
        // console.log("Processing item in doHighlight:", JSON.stringify(item));

        // Generate stylesheet for this query item
        generateHilityStylesheet(item, settings.foundSizeIncrease.isOn);

        // Apply decoration to the mark element
        applyDecorationToMark(mark, item);
      },
    };

    if (item.isRegexp) {
      const paramsRegexp = {
        ignoreGroups: 0,
        ...basicParams,
      };

      const caseInsensitive = settings?.caseSens.isOn ? "" : "i";
      // const diacriticInsensitive = settings?.caseSens.isOn ? 'u' : '';

      wa?.markRegExp(
        new RegExp(item.name, "gmu" + caseInsensitive),
        paramsRegexp
      );
    } else {
      const paramsNormal = {
        accuracy: (settings?.completeWordSearch.isOn
          ? "exactly"
          : "partially") as MarkAccuracy,

        ignoreJoiners: true,
        ignorePunctuation: punctuations,
        separateWordSearch: settings?.splitSearch.isOn,
        diacritics: !settings?.diacriticSens.isOn,
        caseSensitive: settings?.caseSens.isOn,
        ...basicParams,
      };

      wa?.mark(item.name, paramsNormal);
    }
  }
};

const doSingleHighlight = (
  item: QueryItem,
  settings: GlobalSettings,
  wa: Mark,
  options?: {
    exclude?: string[];
    isObserver?: boolean;
  }
) => {
  wa.mark(`${item.name}`, {
    accuracy: settings?.completeWordSearch.isOn ? "exactly" : "partially",
    exclude: options?.exclude,
    element: hilityTag,
    iframes: settings?.iframeSearch.isOn,
    ignoreJoiners: true,
    ignorePunctuation: punctuations,
    acrossElements: true,
    separateWordSearch: settings?.splitSearch.isOn,
    diacritics: !settings?.diacriticSens.isOn,
    caseSensitive: settings?.caseSens.isOn,
    each: function (mark: any) {
      // Log the item being processed
      // console.log("Processing item in doSingleHighlight:", JSON.stringify(item));

      // Generate stylesheet for this query item
      generateHilityStylesheet(item, settings.foundSizeIncrease.isOn);

      // Apply decoration to the mark element
      applyDecorationToMark(mark, item);
    },
  });
};

export const doUnmark = () => {
  const body = document.body;

  if (!body) return;

  const wa = new Mark(body as HTMLBodyElement);

  wa.unmark({
    iframes: true,
    done: function () {
      // Clear scrollbar markers when highlights are removed
      clearScrollbarMarkers();
    },
  });

  // Remove the fallback styles when unmarking
  const fallbackStyles = document.getElementById(
    "highlighty-text-decoration-fallbacks"
  );
  if (fallbackStyles) {
    fallbackStyles.remove();
  }
};
